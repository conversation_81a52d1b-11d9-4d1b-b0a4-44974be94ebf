import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fiaranow_flutter/ChooseLanguage.dart';
import 'package:fiaranow_flutter/ChooseUserTypeScreen.dart';
import 'package:fiaranow_flutter/MainPage.dart';
import 'package:fiaranow_flutter/fcm.dart';
import 'package:fiaranow_flutter/firebase_options.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/screens/MapScreen/FullDayReservationSetup.dart';
import 'package:fiaranow_flutter/services/NotificationWorkManager.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:firebase_ui_oauth_google/firebase_ui_oauth_google.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:logging/logging.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'l10n/app_localizations.dart';
import 'screens/ChatScreen.dart';
import 'screens/DriverDocumentsScreen.dart';
import 'screens/DriverNotificationPermissionScreen.dart';
import 'screens/PhoneNumberForm.dart';
import 'screens/Settings/NotificationSettingsScreen.dart';
import 'screens/UpdateRequiredScreen.dart';
import 'screens/VehicleManagementScreen.dart';
import 'services/TripStateService.dart';
import 'services/UserPreferencesService.dart';
import 'states/AppState.dart';
import 'states/AuthState.dart' as fiaranow;
import 'states/DevToolsService.dart';
import 'states/DevToolsServiceExtensions.dart';
import 'states/NavigationState.dart';
import 'states/PermissionsState.dart';
import 'states/ThemeState.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

// Global logger for top-level functions
final Logger _globalLogger = Logger('Main');

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  final Logger logger = Logger('FCM_Background');

  try {
    logger.info('Received background message: ${message.messageId}');

    // MINIMAL processing only - Firebase 2025 best practice
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
    }

    // Schedule heavy work with WorkManager
    await NotificationWorkManager.scheduleNotificationWork(message);

    logger.info('Background message processing delegated to WorkManager');
  } catch (e) {
    logger.severe('Background handler error: $e');

    // Critical fallback: try immediate notification
    try {
      await initializeFlutterNotifications();
      await showNotification(message);
    } catch (fallbackError) {
      logger.severe('Fallback notification failed: $fallbackError');
    }
  }
}

// Global flag to prevent double initialization
bool _hasInitialized = false;

/// Initialize Firebase App Check with platform-specific providers
Future<void> _initializeAppCheck() async {
  // Check emulator preference for debug provider selection
  final prefs = await SharedPreferences.getInstance();
  final useEmulator = prefs.getBool('use_emulator') ?? false;

  if (!kReleaseMode && useEmulator) {
    // Use debug providers for emulator environment
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
      // Web provider not needed as admin panel doesn't use App Check
    );
    _globalLogger.info('🛡️ App Check initialized with DEBUG providers for emulator');
  } else if (kDebugMode) {
    // Use debug providers for development on physical devices
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
    );
    _globalLogger.info('🛡️ App Check initialized with DEBUG providers for development');
  } else {
    // Production: Use secure providers
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.playIntegrity,
      appleProvider: AppleProvider.appAttest,
    );
    _globalLogger.info('🛡️ App Check initialized with PRODUCTION providers');
  }
}

void main() async {
  runner() async {
    // Prevent double execution of runner function
    if (_hasInitialized) {
      _globalLogger.warning('⚠️ Runner already initialized, skipping duplicate call');
      return Future.value();
    }
    _hasInitialized = true;
    _globalLogger.info('🔧 Initializing app (runner called for first time)');

    WidgetsFlutterBinding.ensureInitialized();

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize Firebase App Check for enhanced security
    try {
      await _initializeAppCheck();
      _globalLogger.info('🛡️ Firebase App Check initialized successfully');
    } catch (e) {
      _globalLogger.severe('❌ Failed to initialize Firebase App Check: $e');
      // Continue execution - App Check failure shouldn't prevent app startup
    }
    
    // Pass all uncaught Flutter errors to Crashlytics
    FlutterError.onError = (errorDetails) {
      if (kDebugMode) {
        // In debug mode: log to Crashlytics for context, then show red error screen
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
        // Present the error to the developer with the red screen
        FlutterError.presentError(errorDetails);
      } else {
        // In release mode: only log to Crashlytics
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      }
    };
    
    // Pass all uncaught asynchronous errors to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      if (kDebugMode) {
        // In debug mode: log to stderr (shows in red) and let it crash
        developer.log(
          'UNHANDLED ERROR',
          error: error,
          stackTrace: stack,
          level: 1000, // Error level
          name: 'PlatformDispatcher',
        );
        // Return false to let the error propagate and crash
        return false;
      } else {
        // In release mode: handle gracefully
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      }
    };

    // Register the background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Initialize WorkManager for notification processing
    await NotificationWorkManager.initialize();

    // Use 50MB of cache for Firestore
    FirebaseFirestore.instance.settings = const Settings(persistenceEnabled: true, cacheSizeBytes: 50 * 1024 * 1024);

    // DevTools service extensions are registered via the DevToolsServiceExtensions controller

    // Check emulator preference
    final prefs = await SharedPreferences.getInstance();
    final useEmulator = prefs.getBool('use_emulator') ?? false;

    if (!kReleaseMode && useEmulator) {
      // Determine the host for the emulators based on platform
      // Android emulator: ******** (special IP to access host machine)
      // iOS simulator: localhost (runs on same network as host)
      // Physical iOS device: Use Tailscale VPN IP
      String host;

      if (Platform.isAndroid) {
        host = '********';
      } else if (Platform.isIOS) {
        // Use device_info_plus for reliable iOS simulator detection
        final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
        final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;

        // !isPhysicalDevice means it's a simulator
        final isSimulator = !iosInfo.isPhysicalDevice;

        if (isSimulator) {
          host = 'localhost'; // iOS simulator
          _globalLogger.info('🔧 Detected iOS simulator, using localhost');
        } else {
          host = '*************'; // Tailscale VPN IP for physical iPhone
          _globalLogger.info('🔧 Detected physical iOS device, using Tailscale IP: $host');
        }
      } else {
        host = 'localhost'; // Default for other platforms
      }

      // Use emulators only in debug mode if enabled
      try {
        // ⚠️ These calls can only be made once per app lifecycle
        // Subsequent calls will throw "already been started" errors
        // The try-catch handles hot reload/restart scenarios gracefully
        FirebaseFirestore.instance.useFirestoreEmulator(host, 8080);
        FirebaseFunctions.instanceFor(region: 'europe-west3').useFunctionsEmulator(host, 5001);
        FirebaseStorage.instance.useStorageEmulator(host, 9199);
        _globalLogger.info('🔧 Connected to Firebase emulators on $host');
      } catch (e) {
        // This is expected on hot reload/restart - Firebase settings cannot be changed twice
        _globalLogger.warning('⚠️ Firebase emulator already configured (hot reload/restart detected): ${e.toString()}');
      }
    } else if (!kReleaseMode) {
      _globalLogger.warning('🔧 [FIREBASE_CONFIG] ⚠️  Debug mode but emulator preference is false - using production Firebase');
      _globalLogger.warning('🔧 [FIREBASE_CONFIG] ⚠️  To use emulators, set use_emulator preference to true');
    } else {
      _globalLogger.info('🔧 [FIREBASE_CONFIG] 🚀 Release mode - using production Firebase');
    }

    final provider = GoogleProvider(
        clientId: kIsWeb
            ? '*************-r0bgpfdg47bim7gufr46v2mh2jl56uae.apps.googleusercontent.com'
            : Platform.isAndroid
                ? '*************-mhd2h9oqteuj3liji0rl2r8blki29jnq.apps.googleusercontent.com'
                : '*************-si16m58aial4rg6vaa2dv31dunku0huj.apps.googleusercontent.com');
    provider.firebaseAuthProvider.setCustomParameters(const {
      'prompt': 'force_select_account',
    });
    FirebaseUIAuth.configureProviders([
      provider,
    ]);

    // Early states initialization - AgentLoggingService must be first since AppState depends on it
    Get.put(AgentLoggingService());
    Get.put(AppState());
    Get.put(fiaranow.AuthState());
    Get.put(PermissionsState());
    Get.put(ThemeState());
    Get.put(DevToolsServiceExtensions());
    Get.lazyPut(() => TripStateService());
    Get.lazyPut(() => UserPreferencesService());

    // Set up hierarchical logging
    hierarchicalLoggingEnabled = true;
    Logger.root.level = Level.INFO;

    // Add console logging
    Logger.root.onRecord.listen((record) {
      // Determine log level for developer.log
      int logLevel = 0; // Default info level
      if (record.level >= Level.SEVERE) {
        logLevel = 1000; // Error level - shows in red
      } else if (record.level >= Level.WARNING) {
        logLevel = 900; // Warning level
      }
      
      // Use developer.log for proper stderr/stdout routing
      developer.log(
        record.message,
        time: record.time,
        level: logLevel,
        name: record.loggerName,
        error: record.error,
        stackTrace: record.stackTrace,
      );

      // Capture severe and warning logs to Crashlytics
      if (record.level >= Level.WARNING && record.error != null) {
        FirebaseCrashlytics.instance.recordError(record.error, record.stackTrace, fatal: false);
      }
      
      // Log all messages to Crashlytics for context
      FirebaseCrashlytics.instance.log('${record.level.name} ${record.loggerName}: ${record.message}');
    });

    // Log that Crashlytics is initialized
    _globalLogger.info('🔧 Firebase Crashlytics initialized for crash reporting');
    
    // Enable Crashlytics debug logging in debug mode
    if (kDebugMode) {
      // Force enable Crashlytics collection in debug mode
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      _globalLogger.info('🔧 Crashlytics collection enabled in debug mode');
    }

    return SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]).then(
      (_) {
        // ignore: prefer_const_constructors
        const myApp = MyApp();
        runApp(myApp);
        _globalLogger.info('🔧 App started successfully');
      },
    );
  }

  // In debug mode: let errors crash naturally for immediate feedback
  // In release mode: use zone-based error handling for graceful error capture
  if (kDebugMode) {
    // Debug mode: run directly without zone guards
    await runner();
  } else {
    // Release mode: run with zone-based error handling
    runZonedGuarded(() async {
      await runner();
    }, (error, stackTrace) {
      // Catch all unhandled async exceptions in release mode
      _globalLogger.severe('Unhandled async error: $error', error, stackTrace);
      FirebaseCrashlytics.instance.recordError(error, stackTrace, fatal: false);
    });
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final Logger _logger = Logger('MyApp');
  final authState = Get.find<fiaranow.AuthState>();
  final appState = Get.find<AppState>();
  final themeState = Get.find<ThemeState>();

  @override
  void initState() {
    super.initState();

    // Initialize flutter_local_notifications with error handling
    _initializeNotifications();

    // Enhanced foreground message handler
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _logger.info('Foreground message received: ${message.messageId}');

      try {
        _handleForegroundMessage(message);
      } catch (e) {
        _logger.severe('Foreground message handling failed: $e');
        _showFallbackNotification(message);
      }
    });

    // Enhanced message opened app handler
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _logger.info('Message opened app: ${message.messageId}');

      try {
        _handleFirebaseMessageNavigation(message);
      } catch (e) {
        _logger.severe('Message navigation failed: $e');
        // Continue app execution, don't crash
      }
    });

    // Handle initial message with error handling
    _handleInitialMessage();

    // Automatically open the ChooseUserTypeScreen when the user logs in, but only
    // if the user has not yet selected a primary user type
    once(authState.currentMobileUser, (MobileUser? mobileUser) {
      if (mobileUser != null && mobileUser.primaryUserType == null) {
        Get.toNamed('/choose_user_type');
      }
    });

    // Check for app updates
    ever(appState.requiredMinimumVersion, (String minVersion) {
      if (_isUpdateRequired(appState.currentVersion.value, minVersion)) {
        Get.offAll(() => const UpdateRequiredScreen());
      }
    });
  }

  Future<void> _initializeNotifications() async {
    try {
      await initializeFlutterNotifications();
      _logger.info('Notifications initialized successfully');
    } catch (e) {
      _logger.severe('Failed to initialize notifications: $e');
      // Continue app execution - notifications are not critical for app startup
    }
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      await showNotification(message);
    } catch (e) {
      _logger.severe('Failed to show foreground notification: $e');
      // Try fallback notification
      _showFallbackNotification(message);
    }
  }

  Future<void> _showFallbackNotification(RemoteMessage message) async {
    try {
      // Minimal fallback notification
      final title = message.notification?.title ?? 'Notification';
      final body = message.notification?.body ?? 'You have a new notification';

      await flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch,
        title,
        body,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'fallback_channel',
            'Fallback Notifications',
            importance: Importance.high,
            priority: Priority.high,
          ),
        ),
      );
    } catch (e) {
      _logger.severe('Even fallback notification failed: $e');
      // At this point, just log and continue - don't crash the app
    }
  }

  Future<void> _handleInitialMessage() async {
    try {
      RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        _logger.info('Handling initial message: ${initialMessage.messageId}');
        _handleFirebaseMessageNavigation(initialMessage);
      }
    } catch (e) {
      _logger.severe('Failed to handle initial message: $e');
      // Continue app execution
    }
  }

  bool _isUpdateRequired(String currentVersion, String minVersion) {
    final current = currentVersion.split('.').map(int.parse).toList();
    final min = minVersion.split('.').map(int.parse).toList();
    for (int i = 0; i < min.length; i++) {
      if (current[i] < min[i]) return true;
      if (current[i] > min[i]) return false;
    }
    return false;
  }

  /// Handle Firebase message navigation when app is opened from notification 🎯
  void _handleFirebaseMessageNavigation(RemoteMessage message) {
    final data = message.data;
    final type = data['type'];

    _logger.info('📱 Handling Firebase message navigation - Type: $type, Data: $data');

    switch (type) {
      case 'chat_message':
        final sessionId = data['sessionId'];
        final sessionTitle = data['sessionTitle'] ?? message.notification?.title ?? 'Chat Support';

        if (sessionId != null) {
          // Add a small delay to ensure the app is fully initialized
          Future.delayed(const Duration(milliseconds: 500), () {
            _logger.info('📱 Navigating to chat session: $sessionId');
            Get.toNamed('/chat', arguments: {
              'sessionId': sessionId,
              'sessionTitle': sessionTitle,
            });
          });
        }
        break;

      // Add other notification types here in the future
      default:
        _logger.info('📱 Unknown notification type for navigation: $type');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => GetMaterialApp(
          title: 'Fiaranow',
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            fontFamily: 'Poppins',
            textTheme: GoogleFonts.poppinsTextTheme(),
            colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF275095), brightness: Brightness.light),
            useMaterial3: true,
            brightness: Brightness.light,
            primaryColor: const Color(0xFF275095),
            scaffoldBackgroundColor: Colors.white,
            appBarTheme: const AppBarTheme(
              backgroundColor: Color(0xFF275095),
              foregroundColor: Colors.white,
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF275095),
                foregroundColor: Colors.white,
                textStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFFF2F2F2),
                foregroundColor: const Color(0xFF275095),
                textStyle: GoogleFonts.poppins(
                  fontSize: 14,
                ),
              ),
            ),
          ),
          darkTheme: ThemeData(
            fontFamily: 'Poppins',
            textTheme: GoogleFonts.poppinsTextTheme(
              ThemeData.dark().textTheme.apply(
                    bodyColor: Colors.white,
                    displayColor: Colors.white,
                  ),
            ),
            colorScheme: ColorScheme.fromSeed(
              seedColor: const Color(0xFF15273f),
              brightness: Brightness.dark,
            ).copyWith(
              onSurface: Colors.white,
            ),
            useMaterial3: true,
            brightness: Brightness.dark,
            primaryColor: const Color(0xFF15273f),
            scaffoldBackgroundColor: Colors.black,
            appBarTheme: const AppBarTheme(
              backgroundColor: Color(0xFF15273f),
              foregroundColor: Colors.white,
            ),
            elevatedButtonTheme: ElevatedButtonThemeData(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF275095),
                foregroundColor: Colors.white,
                textStyle: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                backgroundColor: const Color(0xFF15273f),
                foregroundColor: Colors.white,
                textStyle: GoogleFonts.poppins(
                  fontSize: 14,
                ),
              ),
            ),
          ),
          themeMode: themeState.flutterThemeMode,
          home: Obx(() {
            return authState.currentUser.value == null
                ? SignInScreen(
                    auth: FirebaseAuth.instanceFor(app: Firebase.app()),
                    showAuthActionSwitch: false,
                    showPasswordVisibilityToggle: true,
                  )
                : const MainPage(title: 'Fiaranow');
          }),
          routes: {
            '/choose_user_type': (context) => const ChooseUserTypeScreen(),
            '/choose_language': (context) => const ChooseLanguage(),
            '/chat': (context) {
              final args = ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
              return ChatScreen(
                sessionId: args['sessionId'] as String,
                sessionTitle: args['sessionTitle'] as String,
              );
            },
            '/edit_vehicle_info': (context) => VehicleManagementScreen(),
            '/driver_documents': (context) => const DriverDocumentsScreen(),
            '/driver_notification_permission': (context) => const DriverNotificationPermissionScreen(),
            '/set_phone_number': (context) => const PhoneNumberForm(),
            '/settings/notifications': (context) => const NotificationSettingsScreen(),
            '/full_day_reservation': (context) => FullDayReservationSetup(
                  onNavigateToTab: (index) {
                    // Navigate to the specified tab after returning to the map screen
                    final navigationState = Get.find<NavigationState>();
                    if (navigationState.pageController != null) {
                      navigationState.pageController!.animateToPage(
                        index,
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  mapController: Get.find<NavigationState>().mapController,
                ),
          },
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('en', 'US'),
            Locale('fr', 'FR'),
          ],
          locale: Get.deviceLocale,
          fallbackLocale: const Locale('fr', 'FR'),
          navigatorObservers: [
            FirebaseAnalyticsObserver(analytics: FirebaseAnalytics.instance),
          ],
        ));
  }
}
