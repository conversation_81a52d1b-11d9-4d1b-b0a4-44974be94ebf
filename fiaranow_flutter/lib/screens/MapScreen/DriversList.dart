import 'package:fiaranow_flutter/config/tenant_config.dart';
import 'package:fiaranow_flutter/l10n/app_localizations.dart';
import 'package:fiaranow_flutter/models/MobileUser.dart';
import 'package:fiaranow_flutter/models/MobileUserTenantState.dart';
import 'package:fiaranow_flutter/models/TripStatus.dart';
import 'package:fiaranow_flutter/models/Vehicle.dart';
import 'package:fiaranow_flutter/models/VehicleLinking.dart';
import 'package:fiaranow_flutter/states/AppState.dart';
import 'package:fiaranow_flutter/states/NavigationState.dart';
import 'package:fiaranow_flutter/widgets/DriverActionButton.dart';
import 'package:fiaranow_flutter/widgets/DriverRatingWidget.dart';
import 'package:flutter/material.dart';
import 'package:geoflutterfire_plus/geoflutterfire_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:logging/logging.dart';

class DriversList extends StatefulWidget {
  final String? tappedDriverUid;
  final Function(String?) onDriverTapped;
  final GoogleMapController? mapController;

  const DriversList({
    super.key,
    required this.tappedDriverUid,
    required this.onDriverTapped,
    required this.mapController,
  });

  @override
  State<DriversList> createState() => _DriversListState();
}

class _DriversListState extends State<DriversList> {
  final Logger _logger = Logger('DriversList');

  Future<Vehicle?> _getDriverVehicle(String driverUid) async {
    try {
      // Get driver's tenant state
      final tenantStateDoc = await MobileUserTenantState.getTenantStatesColl(driverUid).doc(TenantConfig.TENANT_ID).get();

      if (!tenantStateDoc.exists) {
        return null;
      }

      final tenantState = tenantStateDoc.data();
      if (tenantState?.currentVehicleLinkingId == null) {
        return null;
      }

      final linkingId = tenantState!.currentVehicleLinkingId!;

      // Get vehicle linking
      final linkingDoc = await VehicleLinking.vehicleLinkingColl.doc(linkingId).get();

      if (!linkingDoc.exists) {
        return null;
      }

      final linking = linkingDoc.data()!;

      // Get vehicle details
      final vehicleDoc = await Vehicle.vehiclesColl.doc(linking.vehicleId).get();

      if (!vehicleDoc.exists) {
        return null;
      }

      final vehicle = vehicleDoc.data()!;
      return vehicle;
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final navigationState = Get.find<NavigationState>();

    return Column(
      children: [
        // Back button and header
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () {
                      navigationState.showNearbyDrivers.value = false;
                    },
                  ),
                  Expanded(
                    child: Obx(() {
                      final trip = navigationState.currentRiderTrip.value;
                      if (trip == null || trip.routeData == null) {
                        return const SizedBox.shrink();
                      }

                      final duration = trip.routeData!.durationSec;
                      final durationInMinutes = duration ~/ 60;
                      final durationText = durationInMinutes >= 60
                          ? '${durationInMinutes ~/ 60}h${durationInMinutes % 60}mn'
                          : '${durationInMinutes + 1} minutes';

                      return Padding(
                        padding: const EdgeInsets.only(left: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  AppLocalizations.of(context)!.mapScreen_tripDistance(
                                      (trip.routeData!.distanceKm).toStringAsFixed(2)), // "Trip Distance: {distance} km"
                                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Text(
                                  AppLocalizations.of(context)!
                                      .mapScreen_estimatedDuration(durationText), // "Estimated Duration: {duration}"
                                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    }),
                  )
                ],
              ),
            ],
          ),
        ),
        const Divider(height: 1),
        // Drivers list
        Expanded(
          child: Obx(() {
            final appState = Get.find<AppState>();
            final currentTrip = navigationState.currentRiderTrip.value;

            return FutureBuilder<List<Map<String, dynamic>>>(
              future: () async {
                // 🌟 First, create the initial list with driver data
                final driversData = List.generate(
                  navigationState.nearbyDriverDetails.length,
                  (index) {
                    final driver = navigationState.nearbyDriverDetails[index];
                    final driverPosition = navigationState.nearbyDrivers[index];
                    final startPosition = navigationState.startPosition.value!;
                    final distanceInMeters = Geolocator.distanceBetween(
                      startPosition.latitude,
                      startPosition.longitude,
                      driverPosition.latitude,
                      driverPosition.longitude,
                    ).toInt();
                    return {
                      'driver': driver,
                      'position': driverPosition,
                      'distance': distanceInMeters,
                      'rating': 0.0,
                      'tripCount': 0,
                    };
                  },
                );

                // 📊 Fetch tenant states for ratings
                final driverStatesMap = <String, MobileUserTenantState>{};
                for (final driverData in driversData) {
                  final driver = driverData['driver'] as MobileUser;
                  try {
                    final stateDoc =
                        await MobileUserTenantState.getTenantStatesColl(driver.uid).doc(TenantConfig.TENANT_ID).get();
                    if (stateDoc.exists) {
                      final tenantState = stateDoc.data()!;
                      driverStatesMap[driver.uid] = tenantState;
                      driverData['rating'] = tenantState.ratingStats?.averageRating ?? 0.0;
                      driverData['tripCount'] = tenantState.ratingStats?.totalTrips ?? 0;
                    }
                  } catch (e, stackTrace) {
                    _logger.severe('🚨 Error fetching tenant state for driver ${driver.uid}', e, stackTrace);
                    _logger.fine('🚨 Stack trace: ${stackTrace.toString().split('\n').take(3).join('\n')}');
                    // Set default values on error
                    driverData['rating'] = 0.0;
                    driverData['tripCount'] = 0;
                  }
                }

                // 🚗 Filter and sort drivers
                final sortedDrivers = driversData.where((driverData) {
                  if (currentTrip?.skippedDriverIds.contains((driverData['driver']! as MobileUser).uid) ?? false) {
                    return false;
                  }
                  return (driverData['distance']! as int) <= appState.tripConfiguration.value.nearbyDriverListedRadiusMeters;
                }).toList()
                  ..sort((a, b) {
                    // 🌟 Sort by rating first (descending), then by distance (ascending)
                    final ratingCompare = (b['rating'] as double).compareTo(a['rating'] as double);
                    if (ratingCompare != 0) return ratingCompare;
                    return (a['distance'] as int).compareTo(b['distance'] as int);
                  });

                return sortedDrivers;
              }(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                final sortedDrivers = snapshot.data!;

                // Check if trip has passenger count
                final passengerCount = currentTrip?.passengerCount ?? 1;

                if (sortedDrivers.isEmpty) {
                  return Center(
                    child: Text(
                      AppLocalizations.of(context)!.mapScreen_noDriversAvailableNearby, // "No drivers available nearby"
                      style: const TextStyle(fontSize: 16),
                    ),
                  );
                }

                return ListView.separated(
                  separatorBuilder: (context, index) => const Divider(height: 1),
                  itemCount: sortedDrivers.length,
                  itemBuilder: (context, index) {
                    final driver = sortedDrivers[index]['driver'] as MobileUser;
                    final driverPosition = sortedDrivers[index]['position'] as GeoFirePoint;

                    return FutureBuilder<Vehicle?>(
                      future: _getDriverVehicle(driver.uid),
                      builder: (context, vehicleSnapshot) {
                        final vehicle = vehicleSnapshot.data;
                        final driverMaxPassengers = vehicle?.maxPassengers ?? 4;
                        final hasEnoughSeats = driverMaxPassengers >= passengerCount;

                        return Card(
                          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          elevation: widget.tappedDriverUid == driver.uid ? 4 : 1,
                          color:
                              widget.tappedDriverUid == driver.uid ? Theme.of(context).primaryColor.withValues(alpha: 0.1) : null,
                          child: InkWell(
                            onTap: () {
                              if (widget.tappedDriverUid == driver.uid) {
                                widget.onDriverTapped(null);
                                return;
                              }

                              widget.onDriverTapped(driver.uid);

                              // Focus the map on the driver's position
                              widget.mapController?.animateCamera(
                                CameraUpdate.newCameraPosition(
                                  CameraPosition(
                                    target: LatLng(driverPosition.latitude, driverPosition.longitude),
                                    zoom: navigationState.zoomLevel.value,
                                  ),
                                ),
                              );
                            },
                            borderRadius: BorderRadius.circular(8),
                            child: Padding(
                              padding: const EdgeInsets.all(12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      CircleAvatar(
                                        radius: 24,
                                        backgroundColor: Theme.of(context).primaryColor,
                                        child: Text(
                                          driver.ensuredDisplayName[0].toUpperCase(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            // Driver name only
                                            Text(
                                              driver.ensuredDisplayName,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            // 🌟 Display driver rating under the name
                                            const SizedBox(height: 4),
                                            DriverRatingWidget(
                                              rating: sortedDrivers[index]['rating'] as double?,
                                              tripCount: sortedDrivers[index]['tripCount'] as int?,
                                              size: 14,
                                            ),
                                            // Vehicle info
                                            if (vehicle != null) ...[
                                              const SizedBox(height: 2),
                                              Row(
                                                children: [
                                                  Icon(
                                                    Icons.directions_car,
                                                    size: 14,
                                                    color: Colors.grey[600],
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Flexible(
                                                    child: Text(
                                                      '${vehicle.brand} ${vehicle.model} (${vehicle.year})',
                                                      style: TextStyle(
                                                        fontSize: 13,
                                                        color: Colors.grey[700],
                                                      ),
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),
                                  // Seats, distance and button row
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Wrap(
                                          spacing: 12,
                                          runSpacing: 4,
                                          children: [
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.people,
                                                  size: 16,
                                                  color: hasEnoughSeats ? Colors.green : Colors.red,
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  AppLocalizations.of(context)!.mapScreen_seatsAvailable(driverMaxPassengers),
                                                  style: TextStyle(
                                                    fontSize: 13,
                                                    color: hasEnoughSeats ? Colors.green : Colors.red,
                                                    fontWeight: hasEnoughSeats ? FontWeight.normal : FontWeight.bold,
                                                  ),
                                                ),
                                                if (!hasEnoughSeats) ...[
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    AppLocalizations.of(context)!.mapScreen_seatsNeeded(passengerCount),
                                                    style: const TextStyle(
                                                      fontSize: 13,
                                                      color: Colors.red,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                ],
                                              ],
                                            ),
                                            Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(
                                                  Icons.location_on,
                                                  size: 16,
                                                  color: Colors.grey[600],
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  (sortedDrivers[index]['distance'] as int) >= 1000
                                                      ? '${((sortedDrivers[index]['distance'] as int) / 1000).toStringAsFixed(1)} km'
                                                      : '${sortedDrivers[index]['distance']} m',
                                                  style: TextStyle(
                                                    fontSize: 13,
                                                    color: Colors.grey[700],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      // Button on the right
                                      SizedBox(
                                        width: MediaQuery.of(context).size.width * 0.3,
                                        child: currentTrip != null &&
                                                currentTrip.status == TripStatus.requestingDriver &&
                                                currentTrip.uidChosenDriver == driver.uid
                                            ? CancelDriverRequestButton(
                                                trip: currentTrip,
                                                driverUid: driver.uid,
                                                onSuccess: () {
                                                  widget.onDriverTapped(null);
                                                },
                                                width: MediaQuery.of(context).size.width * 0.3,
                                              )
                                            : SelectDriverButton(
                                                label: hasEnoughSeats
                                                    ? AppLocalizations.of(context)!.mapScreen_selectDriverButton
                                                    : AppLocalizations.of(context)!.mapScreen_notEnoughSeats,
                                                tripId: currentTrip?.id,
                                                driver: driver,
                                                analyticsParams: {
                                                  'driver_id': driver.uid,
                                                  'distance': sortedDrivers[index]['distance']?.toString() ?? 'unknown',
                                                  'widget_name': 'drivers_list',
                                                  'has_enough_seats': hasEnoughSeats.toString(),
                                                  'driver_capacity': driverMaxPassengers.toString(),
                                                  'passenger_count': passengerCount.toString(),
                                                },
                                                navigationState: navigationState,
                                                enabled: hasEnoughSeats,
                                                width: MediaQuery.of(context).size.width * 0.3,
                                              ),
                                      ),
                                    ],
                                  ),
                                  if (currentTrip != null &&
                                      currentTrip.status == TripStatus.requestingDriver &&
                                      currentTrip.uidChosenDriver == driver.uid &&
                                      currentTrip.driverRouteData != null &&
                                      currentTrip.driverRouteData!.distanceKm > 0) ...[
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.blue.withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              const Icon(Icons.route, size: 14, color: Colors.blue),
                                              const SizedBox(width: 4),
                                              Text(
                                                AppLocalizations.of(context)!.mapScreen_pickupDistance(
                                                  currentTrip.driverRouteData!.distanceKm.toStringAsFixed(2),
                                                ),
                                                style: const TextStyle(fontSize: 12, color: Colors.blue),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              const Icon(Icons.access_time, size: 14, color: Colors.blue),
                                              const SizedBox(width: 4),
                                              Text(
                                                AppLocalizations.of(context)!.mapScreen_approachingETA(
                                                  Duration(seconds: currentTrip.driverRouteData!.durationSec)
                                                      .inMinutes
                                                      .toString(),
                                                ),
                                                style: const TextStyle(fontSize: 12, color: Colors.blue),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            );
          }),
        ),
      ],
    );
  }
}
