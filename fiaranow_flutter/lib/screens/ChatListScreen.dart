import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../config/tenant_config.dart';
import '../l10n/app_localizations.dart';
import '../models/ChatSession.dart';
import '../states/AuthState.dart';

class ChatListScreen extends StatefulWidget {
  final Function(int) onNavigateToTab;

  const ChatListScreen({super.key, required this.onNavigateToTab});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  final AuthState _authState = Get.find<AuthState>();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Widget build(BuildContext context) {
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showNewChatDialog,
        label: Text(AppLocalizations.of(context)!.chat_new),
        icon: const Icon(Icons.contact_support),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: _firestore
            .collection(TenantConfig.getTenantPath('chat_sessions'))
            .where('participantUids', arrayContains: currentUser.uid)
            .orderBy('lastMessageAt', descending: true)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Center(
              child:
                  Text(AppLocalizations.of(context)!.error_with_details(snapshot.error.toString())), // "Error: ${snapshot.error}"
            );
          }

          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }

          final sessions = snapshot.data!.docs.map((doc) => ChatSession.fromFirestore(doc)).toList();

          if (sessions.isEmpty) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.help_outline,
                      size: 80,
                      color: Colors.grey[500],
                    ),
                    const SizedBox(height: 20),
                    Text(
                      AppLocalizations.of(context)!.chat_title,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.chat_no_conversations,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: Text(
                        AppLocalizations.of(context)!.chat_start_conversation_hint,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey[500]),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return CustomScrollView(
            slivers: [
              SliverAppBar(
                title: Text(
                  AppLocalizations.of(context)!.chat_title,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                floating: true,
                pinned: false,
                snap: true,
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                surfaceTintColor: Colors.transparent,
                elevation: 0,
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final session = sessions[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Card(
                        elevation: 2,
                        margin: const EdgeInsets.symmetric(vertical: 6),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          leading: CircleAvatar(
                            radius: 30,
                            backgroundColor: _getStatusColor(session.status).withValues(alpha: 0.1),
                            child: Icon(
                              _getStatusIcon(session),
                              color: _getStatusColor(session.status),
                              size: 30,
                            ),
                          ),
                          title: Text(
                            session.title,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Text(
                                session.getCategoryDisplayName(),
                                style: TextStyle(
                                  color: _getCategoryColor(session.category),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 4),
                              if (session.lastMessage != null)
                                Text(
                                  session.lastMessage!,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                                ),
                            ],
                          ),
                          trailing: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                _formatDate(session.lastMessageAt),
                                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                              ),
                              if (session.unreadCount != null && session.unreadCount! > 0)
                                Container(
                                  margin: const EdgeInsets.only(top: 4),
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    '${session.unreadCount}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          onTap: () {
                            Get.toNamed('/chat', arguments: {
                              'sessionId': session.id,
                              'sessionTitle': session.title,
                            });
                          },
                        ),
                      ),
                    );
                  },
                  childCount: sessions.length,
                ),
              ),
              const SliverToBoxAdapter(
                child: SizedBox(height: 80), // Padding for the FAB
              )
            ],
          );
        },
      ),
    );
  }

  void _showNewChatDialog() {
    ChatCategory selectedCategory = ChatCategory.supportGeneral;
    String title = '';

    Get.dialog(
      StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Text(AppLocalizations.of(context)!.chat_new_dialog_title, style: Theme.of(context).textTheme.titleLarge),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.chat_subject,
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                onChanged: (value) => title = value,
              ),
              const SizedBox(height: 20),
              DropdownButtonFormField<ChatCategory>(
                value: selectedCategory,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.chat_category,
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                ),
                items: ChatCategory.values.map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(_getCategoryName(category)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => selectedCategory = value);
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text(AppLocalizations.of(context)!.chatDialog_cancel), // "Cancel"
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              onPressed: () async {
                if (title.isNotEmpty) {
                  // Close dialog first
                  Get.back();
                  // Then create chat and navigate
                  await _createNewChat(title, selectedCategory);
                }
              },
              child: Text(AppLocalizations.of(context)!.chat_start),
            ),
          ],
        ),
      ),
      barrierDismissible: true,
    );
  }

  Future<void> _createNewChat(String title, ChatCategory category) async {
    final currentUser = _authState.currentMobileUser.value;
    if (currentUser == null) return;

    try {
      final docRef = await _firestore.collection(TenantConfig.getTenantPath('chat_sessions')).add({
        'participantUids': [currentUser.uid, 'admin'],
        'title': title,
        'category': category.toString().split('.').last,
        'status': ChatStatus.active.toString().split('.').last,
        'createdAt': FieldValue.serverTimestamp(),
        'lastMessageAt': FieldValue.serverTimestamp(),
        'isAdminInitiated': false,
      });

      if (mounted) {
        Get.toNamed('/chat', arguments: {
          'sessionId': docRef.id,
          'sessionTitle': title,
        });
      }
    } catch (e) {
      if (mounted) {
        Get.snackbar(
          'Error',
          AppLocalizations.of(context)!.error_creating_chat(e.toString()),
          backgroundColor: Colors.red,
          colorText: Colors.white,
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  Color _getCategoryColor(ChatCategory category) {
    switch (category) {
      case ChatCategory.supportGeneral:
        return Colors.blue;
      case ChatCategory.supportTrip:
        return Colors.green;
      case ChatCategory.supportPayment:
        return Colors.orange;
      case ChatCategory.supportTechnical:
        return Colors.red;
      case ChatCategory.feedbackFollowup:
        return Colors.purple;
    }
  }

  IconData _getCategoryIcon(ChatCategory category) {
    switch (category) {
      case ChatCategory.supportGeneral:
        return Icons.help;
      case ChatCategory.supportTrip:
        return Icons.directions_car;
      case ChatCategory.supportPayment:
        return Icons.payment;
      case ChatCategory.supportTechnical:
        return Icons.build;
      case ChatCategory.feedbackFollowup:
        return Icons.feedback;
    }
  }

  String _getCategoryName(ChatCategory category) {
    switch (category) {
      case ChatCategory.supportGeneral:
        return AppLocalizations.of(context)!.chat_category_general;
      case ChatCategory.supportTrip:
        return AppLocalizations.of(context)!.chat_category_trip;
      case ChatCategory.supportPayment:
        return AppLocalizations.of(context)!.chat_category_payment;
      case ChatCategory.supportTechnical:
        return AppLocalizations.of(context)!.chat_category_technical;
      case ChatCategory.feedbackFollowup:
        return AppLocalizations.of(context)!.chat_category_feedback;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return AppLocalizations.of(context)!.chat_yesterday; // "Yesterday"
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ${AppLocalizations.of(context)!.chat_daysAgo}'; // "days ago"
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Color _getStatusColor(ChatStatus status) {
    switch (status) {
      case ChatStatus.active:
        return Colors.green;
      case ChatStatus.resolved:
        return Colors.green;
      case ChatStatus.archived:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(ChatSession session) {
    switch (session.status) {
      case ChatStatus.active:
        return _getCategoryIcon(session.category);
      case ChatStatus.resolved:
        return Icons.check;
      case ChatStatus.archived:
        return Icons.archive;
    }
  }
}
