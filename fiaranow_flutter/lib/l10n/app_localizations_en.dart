import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get mainPage_appTitle => 'Fiaranow';

  @override
  String get mainPage_choosePrimaryActivity => 'Choose Your Primary Activity';

  @override
  String get mainPage_rider => 'Rider';

  @override
  String get mainPage_driver => 'Driver';

  @override
  String get mainPage_changeAnytime => 'You can change this at any time in the settings menu, so no need to worry.';

  @override
  String get menuDrawer_menu => 'Menu';

  @override
  String get menuDrawer_logout => 'Logout';

  @override
  String get dialog_logoutConfirmation => 'Are you sure you want to logout?';

  @override
  String get dialog_no => 'No';

  @override
  String get dialog_yes => 'Yes';

  @override
  String get passengerCountDialog_confirm => 'Confirm';

  @override
  String get mainPage_chooseLanguage => 'Choose Language';

  @override
  String get menuDrawer_theme => 'Theme';

  @override
  String get menuDrawer_themeSystem => 'System';

  @override
  String get menuDrawer_themeLight => 'Light';

  @override
  String get menuDrawer_themeDark => 'Dark';

  @override
  String get mainPage_english => 'English';

  @override
  String get mainPage_french => 'French';

  @override
  String get mainPage_welcome => 'Welcome';

  @override
  String get notification_screenTitle => 'Enable Notifications';

  @override
  String get notification_enableButton => 'Enable Notifications';

  @override
  String get notification_description => 'As a driver, you need to enable notifications to receive ride requests and important updates.';

  @override
  String get notification_requiredTitle => 'Notifications Required';

  @override
  String get notification_requiredContent => 'Notifications are required for driver mode. Please enable them in settings.';

  @override
  String get notification_cancel => 'Cancel';

  @override
  String get notification_openSettings => 'Open Settings';

  @override
  String get driverMode_permissionTitle => 'Driver Mode Permissions';

  @override
  String get driverMode_permissionDescription => 'To operate as a driver, we need your permission to send notifications and track your location in the background.';

  @override
  String get driverMode_notificationPermission => 'Notifications for ride requests';

  @override
  String get driverMode_backgroundLocationPermission => 'Background location (Always Allow)';

  @override
  String get driverMode_grantPermissions => 'Grant Permissions';

  @override
  String get driverMode_locationRequiredTitle => 'Location Permission Required';

  @override
  String get driverMode_locationRequiredContent => 'Location permission is required for driver mode. Please enable it in settings.';

  @override
  String get driverMode_backgroundLocationTitle => 'Background Location Required';

  @override
  String get driverMode_backgroundLocationExplanation => 'Driver mode requires \'Always Allow\' location permission to track your location and assign rides even when the app is in the background.';

  @override
  String get driverMode_backgroundLocationInstructions => 'Please follow these steps:';

  @override
  String get driverMode_iosStep1 => 'Tap \'Open Settings\' below';

  @override
  String get driverMode_iosStep2 => 'Select \'Location\' and choose \'Always\'';

  @override
  String get driverMode_iosStep3 => 'Return to the app and try again';

  @override
  String get driverMode_androidStep1 => 'Tap \'Open Settings\' below';

  @override
  String get driverMode_androidStep2 => 'Select \'Location\' and choose \'Allow all the time\'';

  @override
  String get driverMode_androidStep3 => 'Return to the app and try again';

  @override
  String get genericError => 'An error occurred. Please try again.';

  @override
  String get driverProfileForm_screenTitle => 'Driver Profile';

  @override
  String get driverProfileForm_vehicleBrand => 'Vehicle Brand/Make';

  @override
  String get driverProfileForm_vehicleModel => 'Vehicle Model';

  @override
  String get driverProfileForm_vehicleColor => 'Main Color';

  @override
  String get driverProfileForm_vehicleYear => 'Year';

  @override
  String get driverProfileForm_registrationNumber => 'Registration Number';

  @override
  String get driverProfileForm_saveButton => 'Save';

  @override
  String get driverProfileForm_maxPassengers => 'Maximum Passengers';

  @override
  String get mainPage_editDriverProfile => 'Edit Driver Profile';

  @override
  String get mainPage_notADriver => 'Not a Driver';

  @override
  String get mainPage_setPrimaryActivityToDriver => 'You need to set your primary activity to Driver first.';

  @override
  String get mainPage_profile => 'Profile';

  @override
  String get mainPage_maps => 'Map';

  @override
  String get mainPage_history => 'History';

  @override
  String get mainPage_home => 'Home';

  @override
  String get mainPage_primaryActivity => 'Primary Activity';

  @override
  String get mainPage_driverProfile => 'Driver Profile';

  @override
  String get mapScreen_pickupAddressInputText => 'Pick-up address';

  @override
  String get mapScreen_destinationAddressInputText => 'Destination address';

  @override
  String get mapScreen_confirmPickupLocationButton => 'Confirm Pick-up location?';

  @override
  String get mapScreen_confirmDestinationButton => 'Confirm destination?';

  @override
  String get mapScreen_noDriversAvailable => 'No drivers available nearby';

  @override
  String get mapScreen_availableDriversHeader => 'Available Drivers';

  @override
  String get mapScreen_selectDriverButton => 'Select';

  @override
  String get mapScreen_notEnoughSeats => 'Not enough seats';

  @override
  String mapScreen_seatsAvailable(int maxPassengers) {
    return 'Seats: $maxPassengers';
  }

  @override
  String mapScreen_seatsNeeded(int passengerCount) {
    return '(Need $passengerCount)';
  }

  @override
  String get mapScreen_changeAddressesButton => 'Change address(es)';

  @override
  String get mapScreen_showNearbyDriversButton => 'Show Nearby Drivers';

  @override
  String get mapScreen_stopServiceButton => 'Stop Service';

  @override
  String get mapScreen_startServiceButton => 'Start Service';

  @override
  String get mapScreen_checkInternetButton => 'Check your Internet';

  @override
  String get home_welcomeText => 'Welcome to Fiaranow';

  @override
  String get phoneNumberForm_screenTitle => 'Set Phone Number';

  @override
  String get phoneNumberForm_phoneNumber => 'Phone Number';

  @override
  String get phoneNumberForm_saveButton => 'Save';

  @override
  String get mapScreen_driverProfileNeedsConfirmation => 'Driver profile needs confirmation';

  @override
  String mapScreen_tripDistance(String distance) {
    return 'Trip Distance: $distance km';
  }

  @override
  String mapScreen_estimatedDuration(String duration) {
    return 'Estimated Duration: $duration';
  }

  @override
  String get mapScreen_noDriversAvailableNearby => 'No drivers available nearby';

  @override
  String mapScreen_roadDistance(String distance) {
    return 'Road Distance: $distance km';
  }

  @override
  String mapScreen_approachingETA(String eta) {
    return 'Approaching ETA: $eta minutes';
  }

  @override
  String get mapScreen_cancel => 'Cancel';

  @override
  String get mapScreen_tripRequests => 'Trip Requests';

  @override
  String get mapScreen_noTripRequests => 'No trip requests at this time.';

  @override
  String mapScreen_from(String startLat, String startLon) {
    return 'From: $startLat, $startLon';
  }

  @override
  String mapScreen_to(String destLat, String destLon) {
    return 'To: $destLat, $destLon';
  }

  @override
  String mapScreen_tripDistanceLabel(String distance, String eta) {
    return 'Trip distance: $distance km, ETA: $eta minutes';
  }

  @override
  String mapScreen_approachingDistance(String distance, String eta) {
    return 'Approaching distance: $distance km, ETA: $eta minutes';
  }

  @override
  String get mapScreen_accept => 'Accept';

  @override
  String get mapScreen_reject => 'Reject';

  @override
  String get mapScreen_confirmRejection => 'Confirm Rejection';

  @override
  String get mapScreen_confirmRejectionMessage => 'Are you sure you want to reject this trip request?';

  @override
  String get mapScreen_noActiveTrip => 'No active trip.';

  @override
  String get mapScreen_tripControl => 'Trip Control';

  @override
  String get mapScreen_tripHeader => 'Trip';

  @override
  String get mapScreen_youShouldNowBeDriving => 'You should now be driving to approach the client...';

  @override
  String get mapScreen_iHaveArrived => 'I have arrived (Awaiting Passenger)';

  @override
  String get mapScreen_waitingForBothToStart => 'Waiting for both to start the trip...';

  @override
  String get mapScreen_pleaseWaitForPassenger => 'Please wait for the passenger to start the trip as well.';

  @override
  String get mapScreen_tripInProgress => 'Trip is in progress.';

  @override
  String get mapScreen_completeTrip => 'Complete Trip';

  @override
  String get mapScreen_tripCompleted => 'Trip is completed.';

  @override
  String get mapScreen_markAsPaid => 'Mark as Paid';

  @override
  String get mapScreen_pleaseWaitForDriver => 'Please wait for the driver to start the trip as well.';

  @override
  String get mapScreen_changeAddresses => 'Change address(es)';

  @override
  String get mapScreen_showNearbyDrivers => 'Show Nearby Drivers';

  @override
  String get home_choosePrimaryActivity => 'Choose your primary activity';

  @override
  String get home_setPhoneNumber => 'Set your phone number';

  @override
  String get home_allowPushNotifications => 'Allow push notifications';

  @override
  String get home_allowLocationPermission => 'Allow location permission';

  @override
  String get home_getRideNow => 'Get a ride Now';

  @override
  String get home_reserveRide => 'Reserve a ride';

  @override
  String get home_specialOffer => 'Special offer';

  @override
  String get home_reserveCarNoGas => '• Reserve a car for the whole day for €25 (no gas)';

  @override
  String get home_reserveCarWithGas => '• Reserve for the whole day for €75 (including gas)';

  @override
  String get tripDetails_title => 'Trip Details';

  @override
  String get tripDetails_pickupTime => 'Pickup Time';

  @override
  String get tripDetails_distance => 'Distance';

  @override
  String get tripDetails_duration => 'Duration';

  @override
  String get tripDetails_cost => 'Cost';

  @override
  String get tripDetails_passenger => 'Passenger';

  @override
  String get tripDetails_passengerCount => 'Passenger Count';

  @override
  String get tripDetails_status => 'Status';

  @override
  String get tripDetails_createdAt => 'Created At';

  @override
  String get tripDetails_completedAt => 'Completed At';

  @override
  String get tripDetails_cancelledAt => 'Cancelled At';

  @override
  String get tripDetails_startLocation => 'Start Location';

  @override
  String get tripDetails_arrivalLocation => 'Arrival Location';

  @override
  String get tripDetails_driverLocation => 'Driver Location';

  @override
  String get tripDetails_showOnMap => 'Show on the Map';

  @override
  String get mapScreen_locationPermissionTitle => 'Location Permission Required';

  @override
  String get mapScreen_locationPermissionMessageDriver => 'Please allow \"Always\" location permission for the \"Fiaranow\" app to work properly in driver mode.';

  @override
  String get mapScreen_locationPermissionMessageRider => 'Please allow location permission for the \"Fiaranow\" app to work properly.';

  @override
  String get mapScreen_enableLocationButton => 'Enable Location';

  @override
  String get mapScreen_reserveThisTrip => 'Reserve this Trip';

  @override
  String mapScreen_routeSelectionTitle(int count) {
    return 'Select a Route ($count)';
  }

  @override
  String mapScreen_routeReserveTitle(int count) {
    return 'Reserve a Route ($count)';
  }

  @override
  String get mapScreen_reservationDateLabel => 'Date';

  @override
  String get mapScreen_reservationTimeLabel => 'Time';

  @override
  String get mapScreen_confirmReservation => 'Confirm Reservation';

  @override
  String get mapScreen_estimatedTripCost => 'Estimated Trip Cost: ';

  @override
  String get mapScreen_finalTripCost => 'Final Trip Cost:';

  @override
  String get mapScreen_currentTripCost => 'Estimated Trip Cost:';

  @override
  String get mapScreen_sorryTripCancelled => 'Sorry, this trip has been cancelled.';

  @override
  String get mapScreen_driverAssigned => 'A driver has been assigned to you.';

  @override
  String get mapScreen_driverWillBeAssigned => 'A driver will be assigned to you. Please stand-by.';

  @override
  String get mapScreen_driverApproaching => 'Driver is approaching you...';

  @override
  String get mapScreen_waitingTime => 'Waiting time:';

  @override
  String get mapScreen_startTrip => 'Start Trip';

  @override
  String get mapScreen_enjoyTheRide => 'Enjoy the ride!';

  @override
  String get mapScreen_thankYouForRiding => 'Thank you for riding with Fiaranow!';

  @override
  String get mapScreen_tripPaid => 'Trip is paid.';

  @override
  String mapScreen_reserveRoute(int count) {
    return 'Reserve a Route ($count)';
  }

  @override
  String mapScreen_selectRoute(int count) {
    return 'Select a Route ($count)';
  }

  @override
  String get mapScreen_routeWarningMessage => 'During the Trip, the driver could choose a different route depending on the road conditions, traffic, or upon Your request. The Trip cost will update accordingly.';

  @override
  String mapScreen_route(int number) {
    return 'Route $number';
  }

  @override
  String mapScreen_routeDetails(String duration, String distance, String cost) {
    return 'Duration: $duration, Distance: $distance km, Cost: $cost Ar';
  }

  @override
  String get mapScreen_success => 'Success';

  @override
  String get mapScreen_tripReservedSuccessfully => 'Trip reserved successfully.';

  @override
  String get mapScreen_error => 'Error';

  @override
  String get mapScreen_driverAlreadyAssigned => 'You are already assigned to another trip. Please complete or cancel your current trip first.';

  @override
  String get mapScreen_driverNotAvailable => 'You do not meet the availability requirements. Please check your documents and vehicle status.';

  @override
  String get mapScreen_tripNotFound => 'This trip is no longer available.';

  @override
  String get mapScreen_locationNotAvailable => 'Your location is not available. Please enable location services and try again.';

  @override
  String get mapScreen_selectFutureTime => 'Please select a time at least 15 minutes in the future.';

  @override
  String get exit_dialog_title => 'Exit Application';

  @override
  String get exit_dialog_message => 'Are you sure you want to exit the application?';

  @override
  String get mapScreen_choosePaymentMethod => 'Choose a Payment Method';

  @override
  String get mapScreen_cashPayment => 'Cash Payment';

  @override
  String get mapScreen_mobileMoneyPayment => 'Mobile Money Payment';

  @override
  String get mapScreen_chooseRideType => 'Choose Ride Type';

  @override
  String get mapScreen_reserveRide => 'Reserve a ride';

  @override
  String get mapScreen_chooseDateAndTime => 'Choose date and time';

  @override
  String get mapScreen_rideNow => 'Ride now';

  @override
  String get mapScreen_findAvailableDrivers => 'Find available drivers';

  @override
  String get mapScreen_deviceClockInaccurate => 'Your device clock is inaccurate';

  @override
  String get mapScreen_adjustDeviceTimeSettings => 'Please adjust your device time settings to automatic to continue using the app.';

  @override
  String mapScreen_currentTimeDifference(String seconds) {
    return 'Current time difference: $seconds seconds';
  }

  @override
  String mapScreen_deviceClockOff(String seconds) {
    return 'Your device clock is off by $seconds seconds. Consider enabling automatic time settings.';
  }

  @override
  String get mapScreen_dismissClockWarning => 'Dismiss';

  @override
  String get mapScreen_failedToGetRoutes => 'Failed to get routes. Please check your Internet connection.';

  @override
  String get serviceStatusUpdate_startService => 'Start Service';

  @override
  String get serviceStatusUpdate_stopService => 'Stop Service';

  @override
  String get serviceStatusUpdate_whyStarting => 'Why are you starting your service?';

  @override
  String get serviceStatusUpdate_whyStopping => 'Why are you stopping your service?';

  @override
  String get serviceStatusUpdate_reason => 'Reason';

  @override
  String get serviceStatusUpdate_specifyReason => 'Please specify your reason';

  @override
  String get serviceStatusReason_morningServiceStart => 'Morning Service Start';

  @override
  String get serviceStatusReason_eveningServiceStart => 'Evening Service Start';

  @override
  String get serviceStatusReason_lunchBreak => 'Lunch Break';

  @override
  String get serviceStatusReason_prayerBreak => 'Prayer Break';

  @override
  String get serviceStatusReason_fuelRefill => 'Fuel Refill';

  @override
  String get serviceStatusReason_vehicleMaintenance => 'Vehicle Maintenance';

  @override
  String get serviceStatusReason_endOfShift => 'End of Shift';

  @override
  String get serviceStatusReason_emergencyStop => 'Emergency Stop';

  @override
  String get serviceStatusReason_switchActivity => 'Switch Activity';

  @override
  String get serviceStatusReason_appRelaunch => 'App Relaunch';

  @override
  String get serviceStatusReason_custom => 'Custom';

  @override
  String get tripRejectionScreen_title => 'Trip Rejection';

  @override
  String get tripRejectionScreen_selectReason => 'Please select a reason for rejecting this trip:';

  @override
  String get tripRejectionScreen_vehicleMalfunction => 'Vehicle malfunction';

  @override
  String get tripRejectionScreen_tooFarPickup => 'Pick-up location too far';

  @override
  String get tripRejectionScreen_heavyTraffic => 'Heavy traffic in the area';

  @override
  String get tripRejectionScreen_unsafeArea => 'Unsafe area';

  @override
  String get tripRejectionScreen_endingShiftSoon => 'Ending shift soon';

  @override
  String get tripRejectionScreen_vehicleCleaning => 'Vehicle needs cleaning';

  @override
  String get tripRejectionScreen_passengerCapacityFull => 'Passenger capacity full';

  @override
  String get tripRejectionScreen_batteryLow => 'Battery low';

  @override
  String get tripRejectionScreen_weatherConditions => 'Bad weather conditions';

  @override
  String get tripRejectionScreen_custom => 'Other reason (specify)';

  @override
  String get tripRejectionScreen_customReasonLabel => 'Please specify your reason';

  @override
  String get tripRejectionScreen_confirm => 'Confirm Rejection';

  @override
  String get tripRejectionScreen_error => 'Error';

  @override
  String get tripRejectionScreen_pleaseEnterReason => 'Please enter your reason for rejection';

  @override
  String get mapScreen_cancelRequestFailed => 'Failed to cancel the request. Please try again.';

  @override
  String mapScreen_pickupAndTripDistance(String distance) {
    return 'Pickup & Trip distance: $distance km';
  }

  @override
  String mapScreen_pickupDistance(String distance) {
    return 'Pickup distance: $distance km';
  }

  @override
  String get mapScreen_pickupLocation => 'Pick-up location';

  @override
  String get mapScreen_pickupTime => 'Pickup Time';

  @override
  String get mapScreen_destinationLocation => 'Destination location';

  @override
  String get menuDrawer_profileSettings => 'Profile Settings';

  @override
  String get menuDrawer_currentMode => 'Current mode';

  @override
  String get menuDrawer_phoneNumber => 'Phone number';

  @override
  String get menuDrawer_permissions => 'Permissions';

  @override
  String get menuDrawer_pushNotifications => 'Push Notifications';

  @override
  String get menuDrawer_gpsLocation => 'GPS Location';

  @override
  String get menuDrawer_gpsLocationBackground => 'GPS Location (Background)';

  @override
  String get menuDrawer_locationWhileInUseDriverSubtitle => 'Set to \'Always\' for optimal driver experience.';

  @override
  String get menuDrawer_driverDocuments => 'My Documents';

  @override
  String get menuDrawer_driverDocumentsDesc => 'Upload and manage your documents';

  @override
  String get updateRequiredScreen_message => 'An update is required to continue using this app.';

  @override
  String get updateRequiredScreen_updateNow => 'Update Now';

  @override
  String get mapScreen_fullDayReservation => 'Full Day Reservation';

  @override
  String get mapScreen_fullDayReservationPrompt => 'A full day reservation allows you to have a driver at your disposal for the entire day. The driver will pick you up at the specified time and location, and will be available to drive you wherever you need to go throughout the day.';

  @override
  String get mapScreen_fullDayPriceOptions => 'Choose Price Option';

  @override
  String get mapScreen_fullDayFixedPrice => '€75 (including gas)';

  @override
  String get mapScreen_gasIncluded => 'Gas included in price';

  @override
  String get mapScreen_fullDayGasExcluded => '€25 (gas not included)';

  @override
  String get mapScreen_gasNotIncluded => 'You pay for gas separately';

  @override
  String get mapScreen_fullDayReservationSuccess => 'Full day reservation created successfully!';

  @override
  String get auth_accountInUseTitle => 'Account Already in Use';

  @override
  String get auth_accountInUseMessage => 'Your account is already logged in on another device. Do you want to log out from other devices?';

  @override
  String get auth_logoutOtherDevices => 'Yes';

  @override
  String get auth_cancelLogin => 'No';

  @override
  String get auth_forcedLogoutTitle => 'Logged Out';

  @override
  String get auth_forcedLogoutMessage => 'Your account has been logged in on another device.';

  @override
  String get auth_forcedLogoutButton => 'OK';

  @override
  String get tripStatus_preparing => 'Preparing';

  @override
  String get tripStatus_requestingDriver => 'Requesting Driver';

  @override
  String get tripStatus_reserved => 'Reserved';

  @override
  String get tripStatus_driverApproaching => 'Driver Approaching';

  @override
  String get tripStatus_driverAwaiting => 'Driver Awaiting';

  @override
  String get tripStatus_inProgress => 'In Progress';

  @override
  String get tripStatus_completed => 'Completed';

  @override
  String get tripStatus_cancelled => 'Cancelled';

  @override
  String get tripStatus_paid => 'Paid';

  @override
  String get tripDetails_pricingOption => 'Pricing Option';

  @override
  String get tripDetails_priceDetails => 'Price Details';

  @override
  String get tripDetails_locationDetails => 'Location Details';

  @override
  String get tripDetails_unknownDestination => 'Unknown destination';

  @override
  String get tripDetails_deleteTrip => 'Delete Trip';

  @override
  String get history_noTripsYet => 'No trips yet';

  @override
  String get history_title => 'History';

  @override
  String get home_welcome => 'Welcome';

  @override
  String get mapScreen_enjoyFullDayRide => 'Enjoy your full day ride!';

  @override
  String get mapScreen_locationFallback => 'Location';

  @override
  String get mapScreen_destinationFallback => 'Destination';

  @override
  String get auth_networkErrorTitle => 'Network Error';

  @override
  String get auth_networkErrorMessage => 'Unable to complete login due to network issues. Please try again when you have a stable connection.';

  @override
  String get navigationState_error => 'Error';

  @override
  String get navigationState_driverTripRequestsError => 'Error listening to driver trip requests. Please restart the app.';

  @override
  String get navigationState_locationError => 'Location Error';

  @override
  String get navigationState_locationTrackingError => 'Unable to track your location. Please check your location settings and restart the app.';

  @override
  String get appState_connectionStatusTitle => 'Connection Status';

  @override
  String get appState_connectionRestored => 'You are connected to the Internet.';

  @override
  String get appState_connectionLost => 'Your Internet connection is not doing well right now.';

  @override
  String get foregroundService_channelName => 'Trip Service';

  @override
  String get foregroundService_channelDescription => 'Keeps the app alive during an ongoing trip.';

  @override
  String get foregroundService_tripInProgress => 'Trip in progress';

  @override
  String get foregroundService_tripOngoing => 'Your trip is ongoing. Tap to return to the app.';

  @override
  String get menuDrawer_notificationPermanentlyDenied => 'Denied by User';

  @override
  String get tripActionButton_error => 'Error';

  @override
  String get tripActionButton_cancel => 'Cancel';

  @override
  String get tripActionButton_start => 'Start';

  @override
  String get tripActionButton_complete => 'Complete';

  @override
  String get mainPage_chat => 'Help';

  @override
  String get chat_title => 'Support Chats';

  @override
  String get chat_new => 'New Chat';

  @override
  String get chat_no_conversations => 'No conversations yet';

  @override
  String get chat_start_conversation_hint => 'Start a new conversation to get help';

  @override
  String get chat_new_dialog_title => 'Start New Conversation';

  @override
  String get chat_subject => 'Subject';

  @override
  String get chat_category => 'Category';

  @override
  String get chat_start => 'Start Chat';

  @override
  String get chat_category_general => 'General Support';

  @override
  String get chat_category_trip => 'Trip Support';

  @override
  String get chat_category_payment => 'Payment Support';

  @override
  String get chat_category_technical => 'Technical Support';

  @override
  String get chat_category_feedback => 'Feedback Follow-up';

  @override
  String get chat_trip_button => 'Trip';

  @override
  String get related_feedback => 'Related Feedback';

  @override
  String get trip_feedback_title => 'Trip Feedback';

  @override
  String get trip_feedback_button => 'Give Feedback';

  @override
  String get trip_details => 'Trip Details';

  @override
  String get rate_your_trip => 'Rate Your Trip';

  @override
  String get feedback_message => 'Feedback Message';

  @override
  String get feedback_message_hint => 'Tell us about your experience...';

  @override
  String get add_photos => 'Add Photos (Optional)';

  @override
  String get feedback_already_submitted => 'You have already submitted feedback for this trip';

  @override
  String get submit_feedback => 'Submit Feedback';

  @override
  String get max_images_reached => 'Maximum 5 images allowed';

  @override
  String get please_rate_trip => 'Please rate your trip';

  @override
  String get feedback_submitted_success => 'Feedback submitted successfully';

  @override
  String get app_feedback_title => 'Application Feedback';

  @override
  String get app_feedback_description => 'Help us improve the app by sharing your feedback, bug reports, or suggestions.';

  @override
  String get app_feedback_hint => 'Describe the issue or suggestion in detail...';

  @override
  String get current_screen_screenshot => 'Current Screen Screenshot';

  @override
  String get include_screenshot => 'Include screenshot';

  @override
  String get additional_images => 'Additional Images';

  @override
  String get max_5_images => 'Maximum 5 images (5MB each)';

  @override
  String get image_too_large => 'Image size must be less than 5MB';

  @override
  String get please_enter_message => 'Please enter a message';

  @override
  String error_with_details(String details) {
    return 'Error: $details';
  }

  @override
  String error_creating_chat(String error) {
    return 'Error creating chat: $error';
  }

  @override
  String error_loading_feedback(String error) {
    return 'Error loading linked feedback: $error';
  }

  @override
  String error_picking_image(String error) {
    return 'Error picking image: $error';
  }

  @override
  String error_sending_image(String error) {
    return 'Error sending image: $error';
  }

  @override
  String error_checking_feedback(String error) {
    return 'Error checking existing feedback: $error';
  }

  @override
  String error_uploading_image(String error) {
    return 'Error uploading image: $error';
  }

  @override
  String error_submitting_feedback(String error) {
    return 'Error submitting feedback: $error';
  }

  @override
  String error_capturing_screenshot(String error) {
    return 'Error capturing screenshot: $error';
  }

  @override
  String get settings => 'Settings';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get settings_general => 'General';

  @override
  String get tripNotifications => 'Trip Notifications';

  @override
  String get ringtoneNotifications => 'Ringtone Notifications';

  @override
  String get ringtoneNotificationsDesc => 'Play a ringtone for important notifications';

  @override
  String get mainPage_usingDefaultEnabled => 'Using default (enabled)';

  @override
  String get mainPage_usingDefaultDisabled => 'Using default (disabled)';

  @override
  String get mainPage_driverMoving => 'Driver Moving';

  @override
  String get mainPage_driverMovingDesc => 'Get notified when your driver starts heading to pick you up';

  @override
  String get mainPage_driverArrived => 'Driver Arrived';

  @override
  String get mainPage_driverArrivedDesc => 'Get notified when your driver has arrived at pickup location';

  @override
  String get mainPage_paymentCompleted => 'Payment Completed';

  @override
  String get mainPage_paymentCompletedDesc => 'Get notified when your trip payment is processed';

  @override
  String get mainPage_reservations => 'Reservations';

  @override
  String get mainPage_reservationReminders => 'Reservation Reminders';

  @override
  String get mainPage_reservationRemindersDesc => 'Get reminders before your scheduled trips';

  @override
  String get mainPage_ringtonePermissionTitle => 'Enable Ringtone Notifications?';

  @override
  String get mainPage_ringtonePermissionMessage => 'Would you like to receive important trip notifications with a ringtone?';

  @override
  String get mainPage_ringtonePermissionDescription => 'This includes notifications when your driver is on the way or has arrived.';

  @override
  String get mainPage_noThanks => 'No Thanks';

  @override
  String get mainPage_enableRingtone => 'Enable Ringtone';

  @override
  String get passengerCountSlider_title => 'Number of Passengers';

  @override
  String get addVehicle_title => 'Add Vehicle';

  @override
  String get no_documents_uploaded => 'No documents uploaded yet';

  @override
  String get no_vehicles_added => 'No vehicles added yet';

  @override
  String vehicle_capacity_passengers(int maxPassengers) {
    return 'Capacity: $maxPassengers passengers';
  }

  @override
  String get vehicle_currentlyAssigned => 'Currently Assigned';

  @override
  String vehicle_error(String error) {
    return 'Error: $error';
  }

  @override
  String get vehicle_assignedVehicle => 'Assigned Vehicle';

  @override
  String get vehicle_myVehicles => 'My Vehicles';

  @override
  String document_expiresInDays(int days) {
    return 'Expires in $days days';
  }

  @override
  String document_expiresOn(String date) {
    return 'Expires: $date';
  }

  @override
  String get menuDrawer_welcome => 'Welcome';

  @override
  String get mainPage_logout => 'Logout';

  @override
  String get mainPage_menu => 'Menu';

  @override
  String get mainPage_no => 'No';

  @override
  String get mainPage_yes => 'Yes';

  @override
  String get mainPage_save => 'Save';

  @override
  String get mainPage_cancel => 'Cancel';

  @override
  String get mainPage_error => 'Error';

  @override
  String get mainPage_settings => 'Settings';

  @override
  String get mainPage_notificationSettings => 'Notification Settings';

  @override
  String get mainPage_tripNotifications => 'Trip Notifications';

  @override
  String get mainPage_ringtoneNotifications => 'Ringtone Notifications';

  @override
  String get mainPage_ringtoneNotificationsDesc => 'Play a ringtone for important notifications';

  @override
  String get notificationSettings_failedToLoadPreferences => 'Failed to load preferences';

  @override
  String get notificationSettings_checkConnectionAndRetry => 'Please check your connection and try again';

  @override
  String get notificationSettings_retry => 'Retry';

  @override
  String get notificationSettings_loadingPreferences => 'Loading preferences...';

  @override
  String get notificationSettings_general => 'General';

  @override
  String get notificationSettings_tripNotifications => 'Trip Notifications';

  @override
  String get notificationSettings_reservations => 'Reservations';

  @override
  String get notificationSettings_driverMoving => 'Driver Moving';

  @override
  String get notificationSettings_driverMovingDesc => 'Get notified when your driver starts heading to pick you up';

  @override
  String get notificationSettings_driverArrived => 'Driver Arrived';

  @override
  String get notificationSettings_driverArrivedDesc => 'Get notified when your driver has arrived at pickup location';

  @override
  String get notificationSettings_paymentCompleted => 'Payment Completed';

  @override
  String get notificationSettings_paymentCompletedDesc => 'Get notified when your trip payment is processed';

  @override
  String get notificationSettings_reservationReminders => 'Reservation Reminders';

  @override
  String get notificationSettings_reservationRemindersDesc => 'Get reminders before your scheduled trips';

  @override
  String get driverDocuments_unableToOpenDocument => 'Unable to open this document';

  @override
  String get documentDetail_title => 'Document Details';

  @override
  String get documentDetail_notAvailable => 'Document not available';

  @override
  String get documentDetail_failedToLoadImage => 'Failed to load image';

  @override
  String get documentDetail_previewNotAvailable => 'Document preview not available';

  @override
  String get documentDetail_unsupportedFormat => 'Unsupported file format';

  @override
  String get documentDetail_notFound => 'We could not find this document';

  @override
  String get documentDetail_expired => 'This document has expired';

  @override
  String get documentDetail_documentName => 'Document Name';

  @override
  String get documentDetail_uploadDate => 'Upload Date';

  @override
  String get documentDetail_expiryDate => 'Expiry Date';

  @override
  String get documentDetail_notes => 'Notes';

  @override
  String get documentDetail_reviewInformation => 'Review Information';

  @override
  String get documentDetail_reviewedDate => 'Reviewed Date';

  @override
  String get documentDetail_adminNotes => 'Admin Notes';

  @override
  String get documentDetail_documentPreview => 'Document Preview';

  @override
  String get documentUpload_permissionDenied => 'Permission Denied';

  @override
  String get documentUpload_cameraPermissionRequired => 'Camera permission is required to take photos';

  @override
  String get documentUpload_photoLibraryPermissionRequired => 'Photo library permission is required to select photos';

  @override
  String get documentUpload_storagePermissionRequiredPhotos => 'Storage permission is required to select photos';

  @override
  String get documentUpload_storagePermissionRequiredFiles => 'Storage permission is required to select files';

  @override
  String get documentUpload_fileTooLarge => 'File Too Large';

  @override
  String get documentUpload_fileSizeLimit => 'Please select a file smaller than 5MB';

  @override
  String get documentUpload_platformError => 'Platform Error';

  @override
  String documentUpload_deviceError(String error) {
    return 'Device error: $error';
  }

  @override
  String get documentUpload_failedToPickFile => 'Failed to pick file. Please try again.';

  @override
  String get documentUpload_cameraError => 'Camera Error';

  @override
  String get documentUpload_failedToCaptureImage => 'Failed to capture image. Please try again.';

  @override
  String get documentUpload_noFileSelected => 'No File Selected';

  @override
  String get documentUpload_selectDocumentPrompt => 'Please select a document to upload';

  @override
  String get documentUpload_noExpiryDate => 'No Expiry Date';

  @override
  String get documentUpload_selectExpiryDatePrompt => 'Please select an expiry date for the document';

  @override
  String get documentUpload_uploadSuccess => 'Document uploaded successfully';

  @override
  String get documentUpload_uploadFailed => 'Upload Failed';

  @override
  String documentUpload_firebaseError(String error) {
    return 'Firebase error: $error';
  }

  @override
  String get documentUpload_unexpectedError => 'An unexpected error occurred. Please try again.';

  @override
  String get documentUpload_documentType => 'Document Type';

  @override
  String get documentUpload_documentName => 'Document Name';

  @override
  String documentUpload_examplePrefix(String example) {
    return 'e.g., $example';
  }

  @override
  String get documentUpload_enterDocumentName => 'Please enter a document name';

  @override
  String get documentUpload_expiryDate => 'Expiry Date';

  @override
  String get documentUpload_selectExpiryDate => 'Select expiry date';

  @override
  String get documentUpload_notesOptional => 'Notes (Optional)';

  @override
  String get documentUpload_additionalInfoHint => 'Any additional information';

  @override
  String get documentUpload_selectDocument => 'Select Document';

  @override
  String get documentUpload_chooseFile => 'Choose File';

  @override
  String get documentUpload_takePhoto => 'Take Photo';

  @override
  String get documentUpload_acceptedFormats => 'Accepted formats: PDF, JPG, PNG (Max 5MB)';

  @override
  String get paymentDialog_cancel => 'Cancel';

  @override
  String get addVehicle_saveButton => 'Save';

  @override
  String get chatDialog_cancel => 'Cancel';

  @override
  String get chat_yesterday => 'Yesterday';

  @override
  String get chat_daysAgo => 'days ago';

  @override
  String get chat_supportTeam => 'Support Team';

  @override
  String get chat_typeMessage => 'Type a message...';

  @override
  String get feedback_camera => 'Camera';

  @override
  String get feedback_gallery => 'Gallery';

  @override
  String get tripDetails_locationUnknown => 'Unknown';

  @override
  String get tripDetails_fixedPrice => 'Carburant inclus';

  @override
  String get tripDetails_perHour => 'Carburant en sus';

  @override
  String get driverDocuments_title => 'My Documents';

  @override
  String get documentUpload_uploadButton => 'Upload Document';

  @override
  String get vehicleManagement_title => 'My Vehicles';

  @override
  String get vehicleManagement_addButton => 'Add Vehicle';

  @override
  String get documentUpload_title => 'Upload Document';

  @override
  String get driverRating_newDriver => 'New Driver';

  @override
  String get mapScreen_failedToGetPredictions => 'Failed to get place predictions. Please check your Internet connection.';

  @override
  String get mapScreen_failedToGetPlaceDetails => 'Failed to get place details. Please check your Internet connection.';

  @override
  String get mapScreen_loadingMap => 'Loading map...';

  @override
  String get passengerTripControl_calculatingCost => 'Calculating cost...';

  @override
  String passengerTripControl_viewingTrip(String status) {
    return 'Viewing $status trip';
  }

  @override
  String get tripDetails_confirmDelete => 'Confirm Delete';

  @override
  String get tripDetails_confirmDeleteMessage => 'Are you sure you want to delete this trip?';

  @override
  String get tripDetails_cancelButton => 'Cancel';

  @override
  String get tripDetails_deleteButton => 'Delete';

  @override
  String get navigationState_cannotShowTripError => 'Cannot show trip while you have an active trip';
}
