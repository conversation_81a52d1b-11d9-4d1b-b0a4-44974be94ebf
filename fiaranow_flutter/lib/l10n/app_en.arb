{"@@locale": "en", "mainPage_appTitle": "<PERSON><PERSON><PERSON>", "@mainPage_appTitle": {"description": "The title of the application"}, "mainPage_choosePrimaryActivity": "Choose Your Primary Activity", "@mainPage_choosePrimaryActivity": {"description": "Title for the screen where the user chooses their primary activity"}, "mainPage_rider": "Rider", "@mainPage_rider": {"description": "Button text for selecting rider as primary activity"}, "mainPage_driver": "Driver", "@mainPage_driver": {"description": "Button text for selecting driver as primary activity"}, "mainPage_changeAnytime": "You can change this at any time in the settings menu, so no need to worry.", "@mainPage_changeAnytime": {"description": "Text indicating that the user can change their primary activity later"}, "menuDrawer_menu": "<PERSON><PERSON>", "@menuDrawer_menu": {"description": "Text for the menu header in the drawer"}, "menuDrawer_logout": "Logout", "@menuDrawer_logout": {"description": "Text for the logout option in the drawer"}, "dialog_logoutConfirmation": "Are you sure you want to logout?", "@dialog_logoutConfirmation": {"description": "Confirmation message for logout dialog"}, "dialog_no": "No", "@dialog_no": {"description": "Text for the 'No' button in the logout confirmation dialog"}, "dialog_yes": "Yes", "@dialog_yes": {"description": "Text for the 'Yes' button in the logout confirmation dialog"}, "passengerCountDialog_confirm": "Confirm", "@passengerCountDialog_confirm": {"description": "Text for the confirm button in passenger count selection dialog"}, "mainPage_chooseLanguage": "Choose Language", "@mainPage_chooseLanguage": {"description": "Text for the choose language button"}, "menuDrawer_theme": "Theme", "@menuDrawer_theme": {"description": "Label for theme selection in menu drawer"}, "menuDrawer_themeSystem": "System", "@menuDrawer_themeSystem": {"description": "Text for system theme option"}, "menuDrawer_themeLight": "Light", "@menuDrawer_themeLight": {"description": "Text for light theme option"}, "menuDrawer_themeDark": "Dark", "@menuDrawer_themeDark": {"description": "Text for dark theme option"}, "mainPage_english": "English", "@mainPage_english": {"description": "Text for the English language option"}, "mainPage_french": "French", "@mainPage_french": {"description": "Text for the French language option"}, "mainPage_welcome": "Welcome", "@mainPage_welcome": {"description": "Text for the welcome message"}, "notification_screenTitle": "Enable Notifications", "@notification_screenTitle": {"description": "Title for the screen where the user enables notifications"}, "notification_enableButton": "Enable Notifications", "@notification_enableButton": {"description": "Button text for enabling notifications"}, "notification_description": "As a driver, you need to enable notifications to receive ride requests and important updates.", "@notification_description": {"description": "Description text explaining why notifications are needed for drivers"}, "notification_requiredTitle": "Notifications Required", "@notification_requiredTitle": {"description": "Title for the dialog indicating that notifications are required"}, "notification_requiredContent": "Notifications are required for driver mode. Please enable them in settings.", "@notification_requiredContent": {"description": "Content text for the dialog indicating that notifications are required"}, "notification_cancel": "Cancel", "@notification_cancel": {"description": "Text for the cancel button in the dialog"}, "notification_openSettings": "Open Settings", "@notification_openSettings": {"description": "Text for the button to open app settings"}, "driverMode_permissionTitle": "Driver Mode Permissions", "@driverMode_permissionTitle": {"description": "Title for the driver mode permission dialog"}, "driverMode_permissionDescription": "To operate as a driver, we need your permission to send notifications and track your location in the background.", "@driverMode_permissionDescription": {"description": "Description explaining why driver mode needs permissions"}, "driverMode_notificationPermission": "Notifications for ride requests", "@driverMode_notificationPermission": {"description": "Label for notification permission in driver mode"}, "driverMode_backgroundLocationPermission": "Background location (Always Allow)", "@driverMode_backgroundLocationPermission": {"description": "Label for background location permission in driver mode"}, "driverMode_grantPermissions": "Grant Permissions", "@driverMode_grantPermissions": {"description": "Button text to grant all driver permissions"}, "driverMode_locationRequiredTitle": "Location Permission Required", "@driverMode_locationRequiredTitle": {"description": "Title for location permission required dialog"}, "driverMode_locationRequiredContent": "Location permission is required for driver mode. Please enable it in settings.", "@driverMode_locationRequiredContent": {"description": "Content for location permission required dialog"}, "driverMode_backgroundLocationTitle": "Background Location Required", "@driverMode_backgroundLocationTitle": {"description": "Title for background location instructions dialog"}, "driverMode_backgroundLocationExplanation": "Driver mode requires 'Always Allow' location permission to track your location and assign rides even when the app is in the background.", "@driverMode_backgroundLocationExplanation": {"description": "Explanation for why background location is needed"}, "driverMode_backgroundLocationInstructions": "Please follow these steps:", "@driverMode_backgroundLocationInstructions": {"description": "Instructions header for setting background location"}, "driverMode_iosStep1": "Tap 'Open Settings' below", "@driverMode_iosStep1": {"description": "iOS step 1 for background location"}, "driverMode_iosStep2": "Select 'Location' and choose 'Always'", "@driverMode_iosStep2": {"description": "iOS step 2 for background location"}, "driverMode_iosStep3": "Return to the app and try again", "@driverMode_iosStep3": {"description": "iOS step 3 for background location"}, "driverMode_androidStep1": "Tap 'Open Settings' below", "@driverMode_androidStep1": {"description": "Android step 1 for background location"}, "driverMode_androidStep2": "Select 'Location' and choose 'Allow all the time'", "@driverMode_androidStep2": {"description": "Android step 2 for background location"}, "driverMode_androidStep3": "Return to the app and try again", "@driverMode_androidStep3": {"description": "Android step 3 for background location"}, "genericError": "An error occurred. Please try again.", "@genericError": {"description": "Generic error message"}, "driverProfileForm_screenTitle": "Driver Profile", "@driverProfileForm_screenTitle": {"description": "Title for the driver profile form screen"}, "driverProfileForm_vehicleBrand": "Vehicle Brand/Make", "@driverProfileForm_vehicleBrand": {"description": "Label for the vehicle brand/make field"}, "driverProfileForm_vehicleModel": "Vehicle Model", "@driverProfileForm_vehicleModel": {"description": "Label for the vehicle model field"}, "driverProfileForm_vehicleColor": "Main Color", "@driverProfileForm_vehicleColor": {"description": "Label for the vehicle color field"}, "driverProfileForm_vehicleYear": "Year", "@driverProfileForm_vehicleYear": {"description": "Label for the vehicle year field"}, "driverProfileForm_registrationNumber": "Registration Number", "@driverProfileForm_registrationNumber": {"description": "Label for the registration number field"}, "driverProfileForm_saveButton": "Save", "@driverProfileForm_saveButton": {"description": "Text for the save button"}, "driverProfileForm_maxPassengers": "Maximum Passengers", "@driverProfileForm_maxPassengers": {"description": "Label for the maximum passengers field"}, "mainPage_editDriverProfile": "Edit Driver Profile", "@mainPage_editDriverProfile": {"description": "Text for the edit driver profile option in the drawer"}, "mainPage_notADriver": "Not a Driver", "@mainPage_notADriver": {"description": "Title for the snackbar when the user is not a driver"}, "mainPage_setPrimaryActivityToDriver": "You need to set your primary activity to Driver first.", "@mainPage_setPrimaryActivityToDriver": {"description": "Message for the snackbar when the user is not a driver"}, "mainPage_profile": "Profile", "@mainPage_profile": {"description": "Label for the profile tab"}, "mainPage_maps": "Map", "@mainPage_maps": {"description": "Label for the maps tab"}, "mainPage_history": "History", "@mainPage_history": {"description": "Label for the history tab"}, "mainPage_home": "Home", "@mainPage_home": {"description": "Label for the home tab"}, "mainPage_primaryActivity": "Primary Activity", "@mainPage_primaryActivity": {"description": "Text for the primary activity option in the drawer"}, "mainPage_driverProfile": "Driver Profile", "@mainPage_driverProfile": {"description": "Text for the driver profile option in the drawer"}, "mapScreen_pickupAddressInputText": "Pick-up address", "@mapScreen_pickupAddressInputText": {"description": "Hint text for the pick-up address input field"}, "mapScreen_destinationAddressInputText": "Destination address", "@mapScreen_destinationAddressInputText": {"description": "Hint text for the destination address input field"}, "mapScreen_confirmPickupLocationButton": "Confirm Pick-up location?", "@mapScreen_confirmPickupLocationButton": {"description": "Text for the button to confirm pick-up location"}, "mapScreen_confirmDestinationButton": "Confirm destination?", "@mapScreen_confirmDestinationButton": {"description": "Text for the button to confirm destination"}, "mapScreen_noDriversAvailable": "No drivers available nearby", "@mapScreen_noDriversAvailable": {"description": "Text indicating no drivers are available nearby"}, "mapScreen_availableDriversHeader": "Available Drivers", "@mapScreen_availableDriversHeader": {"description": "Header text for the available drivers list"}, "mapScreen_selectDriverButton": "Select", "@mapScreen_selectDriverButton": {"description": "Text for the button to select a driver"}, "mapScreen_notEnoughSeats": "Not enough seats", "@mapScreen_notEnoughSeats": {"description": "Text shown when driver doesn't have enough seats for passengers"}, "mapScreen_seatsAvailable": "Seats: {max<PERSON><PERSON><PERSON><PERSON>}", "@mapScreen_seatsAvailable": {"description": "Text showing number of seats available", "placeholders": {"maxPassengers": {"type": "int", "example": "4"}}}, "mapScreen_seatsNeeded": "(Need {passengerCount})", "@mapScreen_seatsNeeded": {"description": "Text showing number of seats needed", "placeholders": {"passengerCount": {"type": "int", "example": "3"}}}, "mapScreen_changeAddressesButton": "Change address(es)", "@mapScreen_changeAddressesButton": {"description": "Text for the button to change addresses"}, "mapScreen_showNearbyDriversButton": "Show Nearby Drivers", "@mapScreen_showNearbyDriversButton": {"description": "Text for the button to show nearby drivers"}, "mapScreen_stopServiceButton": "Stop Service", "@mapScreen_stopServiceButton": {"description": "Text for the button to stop the service"}, "mapScreen_startServiceButton": "Start Service", "@mapScreen_startServiceButton": {"description": "Text for the button to start the service"}, "mapScreen_checkInternetButton": "Check your Internet", "@mapScreen_checkInternetButton": {"description": "Text for the button to check the internet connection"}, "home_welcomeText": "Welcome to Fiaranow", "@home_welcomeText": {"description": "Welcome text on the home screen"}, "phoneNumberForm_screenTitle": "Set Phone Number", "@phoneNumberForm_screenTitle": {"description": "Title for the phone number form screen"}, "phoneNumberForm_phoneNumber": "Phone Number", "@phoneNumberForm_phoneNumber": {"description": "Label for the phone number field"}, "phoneNumberForm_saveButton": "Save", "@phoneNumberForm_saveButton": {"description": "Text for the save button"}, "mapScreen_driverProfileNeedsConfirmation": "Driver profile needs confirmation", "@mapScreen_driverProfileNeedsConfirmation": {"description": "Text indicating that the driver profile needs confirmation"}, "mapScreen_tripDistance": "Trip Distance: {distance} km", "@mapScreen_tripDistance": {"description": "Text indicating the trip distance", "placeholders": {"distance": {"type": "String", "example": "10.5"}}}, "mapScreen_estimatedDuration": "Estimated Duration: {duration}", "@mapScreen_estimatedDuration": {"description": "Text indicating the estimated duration of the trip", "placeholders": {"duration": {"type": "String", "example": "30 minutes"}}}, "mapScreen_noDriversAvailableNearby": "No drivers available nearby", "@mapScreen_noDriversAvailableNearby": {"description": "Text indicating no drivers are available nearby"}, "mapScreen_roadDistance": "Road Distance: {distance} km", "@mapScreen_roadDistance": {"description": "Text indicating the road distance", "placeholders": {"distance": {"type": "String", "example": "5.0"}}}, "mapScreen_approachingETA": "Approaching ETA: {eta} minutes", "@mapScreen_approachingETA": {"description": "Text indicating the approaching ETA", "placeholders": {"eta": {"type": "String", "example": "15"}}}, "mapScreen_cancel": "Cancel", "@mapScreen_cancel": {"description": "Text for the cancel button"}, "mapScreen_tripRequests": "Trip Requests", "@mapScreen_tripRequests": {"description": "Text for the trip requests header"}, "mapScreen_noTripRequests": "No trip requests at this time.", "@mapScreen_noTripRequests": {"description": "Text indicating no trip requests"}, "mapScreen_from": "From: {startLat}, {startLon}", "@mapScreen_from": {"description": "Text indicating the starting point", "placeholders": {"startLat": {"type": "String", "example": "48.8566"}, "startLon": {"type": "String", "example": "2.3522"}}}, "mapScreen_to": "To: {destLat}, {destLon}", "@mapScreen_to": {"description": "Text indicating the destination", "placeholders": {"destLat": {"type": "String", "example": "48.8566"}, "destLon": {"type": "String", "example": "2.3522"}}}, "mapScreen_tripDistanceLabel": "Trip distance: {distance} km, ETA: {eta} minutes", "@mapScreen_tripDistanceLabel": {"description": "Text indicating the trip distance", "placeholders": {"distance": {"type": "String", "example": "10.5"}, "eta": {"type": "String", "example": "30"}}}, "mapScreen_approachingDistance": "Approaching distance: {distance} km, ETA: {eta} minutes", "@mapScreen_approachingDistance": {"description": "Text indicating the approaching distance", "placeholders": {"distance": {"type": "String", "example": "5.0"}, "eta": {"type": "String", "example": "15"}}}, "mapScreen_accept": "Accept", "@mapScreen_accept": {"description": "Text for the accept button"}, "mapScreen_reject": "Reject", "@mapScreen_reject": {"description": "Text for the reject button"}, "mapScreen_confirmRejection": "Confirm Rejection", "@mapScreen_confirmRejection": {"description": "Text for confirming rejection"}, "mapScreen_confirmRejectionMessage": "Are you sure you want to reject this trip request?", "@mapScreen_confirmRejectionMessage": {"description": "Message for confirming rejection"}, "mapScreen_noActiveTrip": "No active trip.", "@mapScreen_noActiveTrip": {"description": "Text indicating no active trip"}, "mapScreen_tripControl": "Trip Control", "@mapScreen_tripControl": {"description": "Text for the trip control header"}, "mapScreen_tripHeader": "Trip", "@mapScreen_tripHeader": {"description": "Simple trip header text for viewing mode"}, "mapScreen_youShouldNowBeDriving": "You should now be driving to approach the client...", "@mapScreen_youShouldNowBeDriving": {"description": "Text indicating the driver should approach the client"}, "mapScreen_iHaveArrived": "I have arrived (Awaiting Passenger)", "@mapScreen_iHaveArrived": {"description": "Text for the button indicating the driver has arrived"}, "mapScreen_waitingForBothToStart": "Waiting for both to start the trip...", "@mapScreen_waitingForBothToStart": {"description": "Text indicating the trip is waiting to start"}, "mapScreen_pleaseWaitForPassenger": "Please wait for the passenger to start the trip as well.", "@mapScreen_pleaseWaitForPassenger": {"description": "Text indicating to wait for the passenger to start the trip"}, "mapScreen_tripInProgress": "Trip is in progress.", "@mapScreen_tripInProgress": {"description": "Text indicating the trip is in progress"}, "mapScreen_completeTrip": "Complete Trip", "@mapScreen_completeTrip": {"description": "Text for the complete trip button"}, "mapScreen_tripCompleted": "Trip is completed.", "@mapScreen_tripCompleted": {"description": "Text indicating the trip is completed"}, "mapScreen_markAsPaid": "<PERSON> as <PERSON><PERSON>", "@mapScreen_markAsPaid": {"description": "Text for the mark as paid button"}, "mapScreen_pleaseWaitForDriver": "Please wait for the driver to start the trip as well.", "@mapScreen_pleaseWaitForDriver": {"description": "Text indicating to wait for the driver to start the trip"}, "mapScreen_changeAddresses": "Change address(es)", "@mapScreen_changeAddresses": {"description": "Text for the button to change addresses"}, "mapScreen_showNearbyDrivers": "Show Nearby Drivers", "@mapScreen_showNearbyDrivers": {"description": "Text for the button to show nearby drivers"}, "home_choosePrimaryActivity": "Choose your primary activity", "@home_choosePrimaryActivity": {"description": "Text for the choose primary activity option"}, "home_setPhoneNumber": "Set your phone number", "@home_setPhoneNumber": {"description": "Text for the set phone number option"}, "home_allowPushNotifications": "Allow push notifications", "@home_allowPushNotifications": {"description": "Text for the allow push notifications option"}, "home_allowLocationPermission": "Allow location permission", "@home_allowLocationPermission": {"description": "Text for the allow location permission option"}, "home_getRideNow": "Get a ride Now", "@home_getRideNow": {"description": "Text for the get a ride now button"}, "home_reserveRide": "Reserve a ride", "@home_reserveRide": {"description": "Text for the reserve a ride button"}, "home_specialOffer": "Special offer", "@home_specialOffer": {"description": "Text for the special offer header"}, "home_reserveCarNoGas": "• Reserve a car for the whole day for €25 (no gas)", "@home_reserveCarNoGas": {"description": "Text for the reserve car no gas offer"}, "home_reserveCarWithGas": "• Reserve for the whole day for €75 (including gas)", "@home_reserveCarWithGas": {"description": "Text for the reserve car with gas offer"}, "tripDetails_title": "Trip Details", "@tripDetails_title": {"description": "Title for the trip details screen"}, "tripDetails_pickupTime": "Pickup Time", "@tripDetails_pickupTime": {"description": "Label for the pickup time"}, "tripDetails_distance": "Distance", "@tripDetails_distance": {"description": "Label for the distance"}, "tripDetails_duration": "Duration", "@tripDetails_duration": {"description": "Label for the duration"}, "tripDetails_cost": "Cost", "@tripDetails_cost": {"description": "Label for the cost"}, "tripDetails_passenger": "Passenger", "@tripDetails_passenger": {"description": "Label for the passenger"}, "tripDetails_passengerCount": "Passenger Count", "@tripDetails_passengerCount": {"description": "Label for the number of passengers in the trip"}, "tripDetails_status": "Status", "@tripDetails_status": {"description": "Label for the status"}, "tripDetails_createdAt": "Created At", "@tripDetails_createdAt": {"description": "Label for the created at"}, "tripDetails_completedAt": "Completed At", "@tripDetails_completedAt": {"description": "Label for the completed at"}, "tripDetails_cancelledAt": "Cancelled At", "@tripDetails_cancelledAt": {"description": "Label for the cancelled at"}, "tripDetails_startLocation": "Start Location", "@tripDetails_startLocation": {"description": "Label for the start location"}, "tripDetails_arrivalLocation": "Arrival Location", "@tripDetails_arrivalLocation": {"description": "Label for the arrival location"}, "tripDetails_driverLocation": "Driver Location", "@tripDetails_driverLocation": {"description": "Label for the driver location"}, "tripDetails_showOnMap": "Show on the Map", "@tripDetails_showOnMap": {"description": "Button text to show the trip on the map"}, "mapScreen_locationPermissionTitle": "Location Permission Required", "@mapScreen_locationPermissionTitle": {"description": "Title for the location permission dialog"}, "mapScreen_locationPermissionMessageDriver": "Please allow \"Always\" location permission for the \"Fiaranow\" app to work properly in driver mode.", "@mapScreen_locationPermissionMessageDriver": {"description": "Location permission message for driver mode"}, "mapScreen_locationPermissionMessageRider": "Please allow location permission for the \"Fiaranow\" app to work properly.", "@mapScreen_locationPermissionMessageRider": {"description": "Location permission message for rider mode"}, "mapScreen_enableLocationButton": "Enable Location", "@mapScreen_enableLocationButton": {"description": "Button text to enable location services"}, "mapScreen_reserveThisTrip": "Reserve this Trip", "@mapScreen_reserveThisTrip": {"description": "Button text for trip reservation"}, "mapScreen_routeSelectionTitle": "Select a Route ({count})", "@mapScreen_routeSelectionTitle": {"description": "Title for route selection", "placeholders": {"count": {"type": "int", "example": "3"}}}, "mapScreen_routeReserveTitle": "Reserve a Route ({count})", "@mapScreen_routeReserveTitle": {"description": "Title for route reservation", "placeholders": {"count": {"type": "int", "example": "3"}}}, "mapScreen_reservationDateLabel": "Date", "@mapScreen_reservationDateLabel": {"description": "Label for reservation date field"}, "mapScreen_reservationTimeLabel": "Time", "@mapScreen_reservationTimeLabel": {"description": "Label for reservation time field"}, "mapScreen_confirmReservation": "Confirm Reservation", "@mapScreen_confirmReservation": {"description": "Button text to confirm reservation"}, "mapScreen_estimatedTripCost": "Estimated Trip Cost: ", "@mapScreen_estimatedTripCost": {"description": "Label for estimated trip cost"}, "mapScreen_finalTripCost": "Final Trip Cost:", "@mapScreen_finalTripCost": {"description": "Label for the final trip cost"}, "mapScreen_currentTripCost": "Estimated Trip Cost:", "@mapScreen_currentTripCost": {"description": "Label for the estimated trip cost"}, "mapScreen_sorryTripCancelled": "Sorry, this trip has been cancelled.", "@mapScreen_sorryTripCancelled": {"description": "Text indicating the trip has been cancelled"}, "mapScreen_driverAssigned": "A driver has been assigned to you.", "@mapScreen_driverAssigned": {"description": "Text indicating a driver has been assigned"}, "mapScreen_driverWillBeAssigned": "A driver will be assigned to you. Please stand-by.", "@mapScreen_driverWillBeAssigned": {"description": "Text indicating a driver will be assigned"}, "mapScreen_driverApproaching": "Driver is approaching you...", "@mapScreen_driverApproaching": {"description": "Text indicating the driver is approaching"}, "mapScreen_waitingTime": "Waiting time:", "@mapScreen_waitingTime": {"description": "Text indicating the waiting time"}, "mapScreen_startTrip": "Start Trip", "@mapScreen_startTrip": {"description": "Text for the start trip button"}, "mapScreen_enjoyTheRide": "Enjoy the ride!", "@mapScreen_enjoyTheRide": {"description": "Text indicating to enjoy the ride"}, "mapScreen_thankYouForRiding": "Thank you for riding with <PERSON><PERSON><PERSON>!", "@mapScreen_thankYouForRiding": {"description": "Text for thanking the user for riding with <PERSON><PERSON><PERSON>"}, "mapScreen_tripPaid": "Trip is paid.", "@mapScreen_tripPaid": {"description": "Text indicating the trip is paid"}, "mapScreen_reserveRoute": "Reserve a Route ({count})", "@mapScreen_reserveRoute": {"description": "Title for reserving a route", "placeholders": {"count": {"type": "int", "example": "3"}}}, "mapScreen_selectRoute": "Select a Route ({count})", "@mapScreen_selectRoute": {"description": "Title for selecting a route", "placeholders": {"count": {"type": "int", "example": "3"}}}, "mapScreen_routeWarningMessage": "During the Trip, the driver could choose a different route depending on the road conditions, traffic, or upon Your request. The Trip cost will update accordingly.", "@mapScreen_routeWarningMessage": {"description": "Warning message about potential route changes"}, "mapScreen_route": "Route {number}", "@mapScreen_route": {"description": "Route number label", "placeholders": {"number": {"type": "int", "example": "1"}}}, "mapScreen_routeDetails": "Duration: {duration}, Distance: {distance} km, Cost: {cost} Ar", "@mapScreen_routeDetails": {"description": "Route details text", "placeholders": {"duration": {"type": "String", "example": "30 minutes"}, "distance": {"type": "String", "example": "5.2"}, "cost": {"type": "String", "example": "15000"}}}, "mapScreen_success": "Success", "@mapScreen_success": {"description": "Snackbar title for success"}, "mapScreen_tripReservedSuccessfully": "<PERSON> reserved successfully.", "@mapScreen_tripReservedSuccessfully": {"description": "Snackbar message for successful trip reservation"}, "mapScreen_error": "Error", "@mapScreen_error": {"description": "Error message title"}, "mapScreen_driverAlreadyAssigned": "You are already assigned to another trip. Please complete or cancel your current trip first.", "@mapScreen_driverAlreadyAssigned": {"description": "Error message shown when driver is already assigned to another trip"}, "mapScreen_driverNotAvailable": "You do not meet the availability requirements. Please check your documents and vehicle status.", "@mapScreen_driverNotAvailable": {"description": "Error message shown when driver doesn't meet availability requirements"}, "mapScreen_tripNotFound": "This trip is no longer available.", "@mapScreen_tripNotFound": {"description": "Error message shown when trip is not found or no longer available"}, "mapScreen_locationNotAvailable": "Your location is not available. Please enable location services and try again.", "@mapScreen_locationNotAvailable": {"description": "Error message shown when driver's location is not available"}, "mapScreen_selectFutureTime": "Please select a time at least 15 minutes in the future.", "@mapScreen_selectFutureTime": {"description": "Message shown when selected time is too soon"}, "exit_dialog_title": "Exit Application", "@exit_dialog_title": {"description": "Title of the exit confirmation dialog"}, "exit_dialog_message": "Are you sure you want to exit the application?", "@exit_dialog_message": {"description": "Message shown in the exit confirmation dialog"}, "mapScreen_choosePaymentMethod": "Choose a Payment Method", "@mapScreen_choosePaymentMethod": {"description": "Title for payment method selection dialog"}, "mapScreen_cashPayment": "Cash Payment", "@mapScreen_cashPayment": {"description": "Option for cash payment"}, "mapScreen_mobileMoneyPayment": "Mobile Money Payment", "@mapScreen_mobileMoneyPayment": {"description": "Option for mobile money payment"}, "mapScreen_chooseRideType": "Choose Ride Type", "@mapScreen_chooseRideType": {"description": "Title for ride type selection dialog"}, "mapScreen_reserveRide": "Reserve a ride", "@mapScreen_reserveRide": {"description": "Option to reserve a ride"}, "mapScreen_chooseDateAndTime": "Choose date and time", "@mapScreen_chooseDateAndTime": {"description": "Subtitle for reserve ride option"}, "mapScreen_rideNow": "Ride now", "@mapScreen_rideNow": {"description": "Option to ride now"}, "mapScreen_findAvailableDrivers": "Find available drivers", "@mapScreen_findAvailableDrivers": {"description": "Subtitle for ride now option"}, "mapScreen_deviceClockInaccurate": "Your device clock is inaccurate", "@mapScreen_deviceClockInaccurate": {"description": "Title shown when device clock has significant drift"}, "mapScreen_adjustDeviceTimeSettings": "Please adjust your device time settings to automatic to continue using the app.", "@mapScreen_adjustDeviceTimeSettings": {"description": "Message asking user to adjust time settings"}, "mapScreen_currentTimeDifference": "Current time difference: {seconds} seconds", "@mapScreen_currentTimeDifference": {"description": "Shows the current time drift in seconds", "placeholders": {"seconds": {"type": "String", "example": "10.5"}}}, "mapScreen_deviceClockOff": "Your device clock is off by {seconds} seconds. Consider enabling automatic time settings.", "@mapScreen_deviceClockOff": {"description": "Warning message for smaller clock drift", "placeholders": {"seconds": {"type": "String", "example": "7.2"}}}, "mapScreen_dismissClockWarning": "<PERSON><PERSON><PERSON>", "@mapScreen_dismissClockWarning": {"description": "<PERSON><PERSON> text to dismiss the device clock warning dialog"}, "mapScreen_failedToGetRoutes": "Failed to get routes. Please check your Internet connection.", "@mapScreen_failedToGetRoutes": {"description": "Error message shown when route fetching fails"}, "serviceStatusUpdate_startService": "Start Service", "@serviceStatusUpdate_startService": {"description": "Title for starting the service"}, "serviceStatusUpdate_stopService": "Stop Service", "@serviceStatusUpdate_stopService": {"description": "Title for stopping the service"}, "serviceStatusUpdate_whyStarting": "Why are you starting your service?", "@serviceStatusUpdate_whyStarting": {"description": "Prompt asking why the service is being started"}, "serviceStatusUpdate_whyStopping": "Why are you stopping your service?", "@serviceStatusUpdate_whyStopping": {"description": "Prompt asking why the service is being stopped"}, "serviceStatusUpdate_reason": "Reason", "@serviceStatusUpdate_reason": {"description": "Label for the reason input field"}, "serviceStatusUpdate_specifyReason": "Please specify your reason", "@serviceStatusUpdate_specifyReason": {"description": "Prompt to specify a custom reason"}, "serviceStatusReason_morningServiceStart": "Morning Service Start", "@serviceStatusReason_morningServiceStart": {"description": "Reason for starting service in the morning"}, "serviceStatusReason_eveningServiceStart": "Evening Service Start", "@serviceStatusReason_eveningServiceStart": {"description": "Reason for starting service in the evening"}, "serviceStatusReason_lunchBreak": "Lunch Break", "@serviceStatusReason_lunchBreak": {"description": "Reason for taking a lunch break"}, "serviceStatusReason_prayerBreak": "Prayer Break", "@serviceStatusReason_prayerBreak": {"description": "Reason for taking a prayer break"}, "serviceStatusReason_fuelRefill": "Fuel Refill", "@serviceStatusReason_fuelRefill": {"description": "Reason for refilling fuel"}, "serviceStatusReason_vehicleMaintenance": "Vehicle Maintenance", "@serviceStatusReason_vehicleMaintenance": {"description": "Reason for vehicle maintenance"}, "serviceStatusReason_endOfShift": "End of Shift", "@serviceStatusReason_endOfShift": {"description": "Reason for ending the shift"}, "serviceStatusReason_emergencyStop": "Emergency Stop", "@serviceStatusReason_emergencyStop": {"description": "Reason for an emergency stop"}, "serviceStatusReason_switchActivity": "Switch Activity", "@serviceStatusReason_switchActivity": {"description": "Reason for switching activity"}, "serviceStatusReason_appRelaunch": "App Relaunch", "@serviceStatusReason_appRelaunch": {"description": "Reason for relaunching the app"}, "serviceStatusReason_custom": "Custom", "@serviceStatusReason_custom": {"description": "Custom reason specified by the user"}, "tripRejectionScreen_title": "Trip Rejection", "@tripRejectionScreen_title": {"description": "Title of the trip rejection screen"}, "tripRejectionScreen_selectReason": "Please select a reason for rejecting this trip:", "@tripRejectionScreen_selectReason": {"description": "Message asking the driver to select a reason for rejecting the trip"}, "tripRejectionScreen_vehicleMalfunction": "Vehicle malfunction", "@tripRejectionScreen_vehicleMalfunction": {"description": "Option for rejecting trip due to vehicle malfunction"}, "tripRejectionScreen_tooFarPickup": "Pick-up location too far", "@tripRejectionScreen_tooFarPickup": {"description": "Option for rejecting trip due to distant pickup location"}, "tripRejectionScreen_heavyTraffic": "Heavy traffic in the area", "@tripRejectionScreen_heavyTraffic": {"description": "Option for rejecting trip due to heavy traffic"}, "tripRejectionScreen_unsafeArea": "Unsafe area", "@tripRejectionScreen_unsafeArea": {"description": "Option for rejecting trip due to unsafe area"}, "tripRejectionScreen_endingShiftSoon": "Ending shift soon", "@tripRejectionScreen_endingShiftSoon": {"description": "Option for rejecting trip due to ending shift"}, "tripRejectionScreen_vehicleCleaning": "Vehicle needs cleaning", "@tripRejectionScreen_vehicleCleaning": {"description": "Option for rejecting trip due to vehicle needing cleaning"}, "tripRejectionScreen_passengerCapacityFull": "Passenger capacity full", "@tripRejectionScreen_passengerCapacityFull": {"description": "Option for rejecting trip due to full passenger capacity"}, "tripRejectionScreen_batteryLow": "Battery low", "@tripRejectionScreen_batteryLow": {"description": "Option for rejecting trip due to low battery"}, "tripRejectionScreen_weatherConditions": "Bad weather conditions", "@tripRejectionScreen_weatherConditions": {"description": "Option for rejecting trip due to bad weather"}, "tripRejectionScreen_custom": "Other reason (specify)", "@tripRejectionScreen_custom": {"description": "Option for rejecting trip with a custom reason"}, "tripRejectionScreen_customReasonLabel": "Please specify your reason", "@tripRejectionScreen_customReasonLabel": {"description": "Label for the custom reason text input field"}, "tripRejectionScreen_confirm": "Confirm Rejection", "@tripRejectionScreen_confirm": {"description": "Text for the confirm rejection button"}, "tripRejectionScreen_error": "Error", "@tripRejectionScreen_error": {"description": "Title for error messages"}, "tripRejectionScreen_pleaseEnterReason": "Please enter your reason for rejection", "@tripRejectionScreen_pleaseEnterReason": {"description": "Error message shown when no custom reason is provided"}, "mapScreen_cancelRequestFailed": "Failed to cancel the request. Please try again.", "@mapScreen_cancelRequestFailed": {"description": "Error message shown when canceling a request fails"}, "mapScreen_pickupAndTripDistance": "Pickup & Trip distance: {distance} km", "@mapScreen_pickupAndTripDistance": {"description": "Text showing both pickup and trip distances", "placeholders": {"distance": {"type": "String", "example": "10.5"}}}, "mapScreen_pickupDistance": "Pickup distance: {distance} km", "@mapScreen_pickupDistance": {"description": "Text showing just the pickup distance", "placeholders": {"distance": {"type": "String", "example": "5.2"}}}, "mapScreen_pickupLocation": "Pick-up location", "@mapScreen_pickupLocation": {"description": "Label for the pickup location"}, "mapScreen_pickupTime": "Pickup Time", "@mapScreen_pickupTime": {"description": "Label for the scheduled pickup time"}, "mapScreen_destinationLocation": "Destination location", "@mapScreen_destinationLocation": {"description": "Label for the destination location"}, "menuDrawer_profileSettings": "Profile Settings", "@menuDrawer_profileSettings": {"description": "Section header for profile settings in the menu drawer"}, "menuDrawer_currentMode": "Current mode", "@menuDrawer_currentMode": {"description": "Label for the current user mode (driver/rider) setting"}, "menuDrawer_phoneNumber": "Phone number", "@menuDrawer_phoneNumber": {"description": "Label for the phone number setting"}, "menuDrawer_permissions": "Permissions", "@menuDrawer_permissions": {"description": "Section header for permissions in the menu drawer"}, "menuDrawer_pushNotifications": "Push Notifications", "@menuDrawer_pushNotifications": {"description": "Label for push notifications permission setting"}, "menuDrawer_gpsLocation": "GPS Location", "@menuDrawer_gpsLocation": {"description": "Menu item for GPS location permission"}, "menuDrawer_gpsLocationBackground": "GPS Location (Background)", "@menuDrawer_gpsLocationBackground": {"description": "Menu item for GPS location permission (background for drivers)"}, "menuDrawer_locationWhileInUseDriverSubtitle": "Set to 'Always' for optimal driver experience.", "@menuDrawer_locationWhileInUseDriverSubtitle": {"description": "Subtitle for GPS location when driver has 'while in use' permission."}, "menuDrawer_driverDocuments": "My Documents", "@menuDrawer_driverDocuments": {"description": "Menu item for drivers to access their documents"}, "menuDrawer_driverDocumentsDesc": "Upload and manage your documents", "@menuDrawer_driverDocumentsDesc": {"description": "Subtitle description for driver documents menu item"}, "updateRequiredScreen_message": "An update is required to continue using this app.", "@updateRequiredScreen_message": {"description": "Message in UpdateRequiredScreen indicating that an update is required to continue using the app"}, "updateRequiredScreen_updateNow": "Update Now", "@updateRequiredScreen_updateNow": {"description": "Button text in UpdateRequiredScreen to initiate the update process"}, "mapScreen_fullDayReservation": "Full Day Reservation", "@mapScreen_fullDayReservation": {"description": "Title for the full day reservation dialog"}, "mapScreen_fullDayReservationPrompt": "A full day reservation allows you to have a driver at your disposal for the entire day. The driver will pick you up at the specified time and location, and will be available to drive you wherever you need to go throughout the day.", "@mapScreen_fullDayReservationPrompt": {"description": "Message explaining what a full day reservation is"}, "mapScreen_fullDayPriceOptions": "Choose Price Option", "@mapScreen_fullDayPriceOptions": {"description": "Title for the full day price options dialog"}, "mapScreen_fullDayFixedPrice": "€75 (including gas)", "@mapScreen_fullDayFixedPrice": {"description": "Option for full day reservation with gas included"}, "mapScreen_gasIncluded": "Gas included in price", "@mapScreen_gasIncluded": {"description": "Text indicating that gas is included in the price"}, "mapScreen_fullDayGasExcluded": "€25 (gas not included)", "@mapScreen_fullDayGasExcluded": {"description": "Option for full day reservation without gas"}, "mapScreen_gasNotIncluded": "You pay for gas separately", "@mapScreen_gasNotIncluded": {"description": "Text indicating that gas is not included in the price"}, "mapScreen_fullDayReservationSuccess": "Full day reservation created successfully!", "@mapScreen_fullDayReservationSuccess": {"description": "Success message shown after creating a full day reservation"}, "auth_accountInUseTitle": "Account Already in Use", "@auth_accountInUseTitle": {"description": "Title for the dialog shown when account is already logged in on another device"}, "auth_accountInUseMessage": "Your account is already logged in on another device. Do you want to log out from other devices?", "@auth_accountInUseMessage": {"description": "Message asking if user wants to log out from other devices"}, "auth_logoutOtherDevices": "Yes", "@auth_logoutOtherDevices": {"description": "Button text to confirm logging out from other devices"}, "auth_cancelLogin": "No", "@auth_cancelLogin": {"description": "Button text to cancel login and not log out from other devices"}, "auth_forcedLogoutTitle": "Logged Out", "@auth_forcedLogoutTitle": {"description": "Title for the dialog shown when user is forced to log out"}, "auth_forcedLogoutMessage": "Your account has been logged in on another device.", "@auth_forcedLogoutMessage": {"description": "Message shown when user is forced to log out because of login on another device"}, "auth_forcedLogoutButton": "OK", "@auth_forcedLogoutButton": {"description": "Button text to acknowledge forced logout"}, "tripStatus_preparing": "Preparing", "@tripStatus_preparing": {"description": "Trip status when the trip is being prepared"}, "tripStatus_requestingDriver": "Requesting Driver", "@tripStatus_requestingDriver": {"description": "Trip status when searching for a driver"}, "tripStatus_reserved": "Reserved", "@tripStatus_reserved": {"description": "Trip status when the trip is reserved"}, "tripStatus_driverApproaching": "Driver Approaching", "@tripStatus_driverApproaching": {"description": "Trip status when the driver is approaching"}, "tripStatus_driverAwaiting": "Driver Awaiting", "@tripStatus_driverAwaiting": {"description": "Trip status when the driver is waiting for passenger"}, "tripStatus_inProgress": "In Progress", "@tripStatus_inProgress": {"description": "Trip status when the trip is ongoing"}, "tripStatus_completed": "Completed", "@tripStatus_completed": {"description": "Trip status when the trip is completed"}, "tripStatus_cancelled": "Cancelled", "@tripStatus_cancelled": {"description": "Trip status when the trip is cancelled"}, "tripStatus_paid": "Paid", "@tripStatus_paid": {"description": "Trip status when the trip is paid"}, "tripDetails_pricingOption": "Pricing Option", "@tripDetails_pricingOption": {"description": "Label for the pricing option in trip details"}, "tripDetails_priceDetails": "Price Details", "@tripDetails_priceDetails": {"description": "Title for the price details section in trip details"}, "tripDetails_locationDetails": "Location Details", "@tripDetails_locationDetails": {"description": "Title for the location details section in trip details"}, "tripDetails_unknownDestination": "Unknown destination", "@tripDetails_unknownDestination": {"description": "Text shown when the destination is unknown"}, "tripDetails_deleteTrip": "Delete Trip", "@tripDetails_deleteTrip": {"description": "But<PERSON> text to delete a trip"}, "history_noTripsYet": "No trips yet", "@history_noTripsYet": {"description": "Message displayed when the user has no trips in their history"}, "history_title": "History", "@history_title": {"description": "Title for the trip history screen"}, "home_welcome": "Welcome", "@home_welcome": {"description": "A welcome message on the home screen"}, "mapScreen_enjoyFullDayRide": "Enjoy your full day ride!", "@mapScreen_enjoyFullDayRide": {"description": "Text shown to passengers during a full day reservation trip"}, "mapScreen_locationFallback": "Location", "@mapScreen_locationFallback": {"description": "Fallback text when pickup location name is not available"}, "mapScreen_destinationFallback": "Destination", "@mapScreen_destinationFallback": {"description": "Fallback text when destination location name is not available"}, "auth_networkErrorTitle": "Network Error", "@auth_networkErrorTitle": {"description": "Title for the snackbar shown when there is a network error during login"}, "auth_networkErrorMessage": "Unable to complete login due to network issues. Please try again when you have a stable connection.", "@auth_networkErrorMessage": {"description": "Message shown when login fails due to network connectivity issues"}, "navigationState_error": "Error", "@navigationState_error": {"description": "General error title used in error messages"}, "navigationState_driverTripRequestsError": "Error listening to driver trip requests. Please restart the app.", "@navigationState_driverTripRequestsError": {"description": "Error message when there's an issue with listening to driver trip requests"}, "navigationState_locationError": "Location Error", "@navigationState_locationError": {"description": "Title for location-related error messages"}, "navigationState_locationTrackingError": "Unable to track your location. Please check your location settings and restart the app.", "@navigationState_locationTrackingError": {"description": "Error message shown when location tracking fails"}, "appState_connectionStatusTitle": "Connection Status", "@appState_connectionStatusTitle": {"description": "Title for connection status messages"}, "appState_connectionRestored": "You are connected to the Internet.", "@appState_connectionRestored": {"description": "Message shown when internet connection is restored"}, "appState_connectionLost": "Your Internet connection is not doing well right now.", "@appState_connectionLost": {"description": "Message shown when internet connection is lost or unstable"}, "foregroundService_channelName": "Trip Service", "@foregroundService_channelName": {"description": "Name of the foreground service notification channel"}, "foregroundService_channelDescription": "Keeps the app alive during an ongoing trip.", "@foregroundService_channelDescription": {"description": "Description of the foreground service notification channel"}, "foregroundService_tripInProgress": "Trip in progress", "@foregroundService_tripInProgress": {"description": "Title of the foreground notification shown during a trip"}, "foregroundService_tripOngoing": "Your trip is ongoing. Tap to return to the app.", "@foregroundService_tripOngoing": {"description": "Text shown in the foreground notification during a trip"}, "menuDrawer_notificationPermanentlyDenied": "Denied by User", "@menuDrawer_notificationPermanentlyDenied": {"description": "Subtitle text for push notifications when permission is permanently denied"}, "tripActionButton_error": "Error", "@tripActionButton_error": {"description": "Error title shown in snackbar when trip action fails"}, "tripActionButton_cancel": "Cancel", "@tripActionButton_cancel": {"description": "Label for cancel trip button"}, "tripActionButton_start": "Start", "@tripActionButton_start": {"description": "Label for start trip button"}, "tripActionButton_complete": "Complete", "@tripActionButton_complete": {"description": "Label for complete trip button"}, "mainPage_chat": "Help", "@mainPage_chat": {"description": "Label for help tab in bottom navigation"}, "chat_title": "Support Chats", "@chat_title": {"description": "Title for chat list screen"}, "chat_new": "New Chat", "@chat_new": {"description": "Button label for creating new chat"}, "chat_no_conversations": "No conversations yet", "@chat_no_conversations": {"description": "Empty state message when no chats exist"}, "chat_start_conversation_hint": "Start a new conversation to get help", "@chat_start_conversation_hint": {"description": "Hint text shown when no chats exist"}, "chat_new_dialog_title": "Start New Conversation", "@chat_new_dialog_title": {"description": "Title for new chat dialog"}, "chat_subject": "Subject", "@chat_subject": {"description": "Label for chat subject/title field"}, "chat_category": "Category", "@chat_category": {"description": "Label for chat category dropdown"}, "chat_start": "Start Chat", "@chat_start": {"description": "Button label to start new chat"}, "chat_category_general": "General Support", "@chat_category_general": {"description": "General support category"}, "chat_category_trip": "Trip Support", "@chat_category_trip": {"description": "Trip-related support category"}, "chat_category_payment": "Payment Support", "@chat_category_payment": {"description": "Payment-related support category"}, "chat_category_technical": "Technical Support", "@chat_category_technical": {"description": "Technical support category"}, "chat_category_feedback": "Feedback Follow-up", "@chat_category_feedback": {"description": "Feedback follow-up category"}, "chat_trip_button": "Trip", "@chat_trip_button": {"description": "Button text to navigate to trip details from chat"}, "related_feedback": "Related Feedback", "@related_feedback": {"description": "Label for linked feedback in chat"}, "trip_feedback_title": "<PERSON>", "@trip_feedback_title": {"description": "Title for trip feedback screen"}, "trip_feedback_button": "<PERSON>", "@trip_feedback_button": {"description": "Button text to open trip feedback screen"}, "trip_details": "Trip Details", "@trip_details": {"description": "Label for trip details section"}, "rate_your_trip": "Rate Your Trip", "@rate_your_trip": {"description": "Header for rating section"}, "feedback_message": "Feedback Message", "@feedback_message": {"description": "Label for feedback message field"}, "feedback_message_hint": "Tell us about your experience...", "@feedback_message_hint": {"description": "Placeholder for feedback message field"}, "add_photos": "Add Photos (Optional)", "@add_photos": {"description": "Label for photo addition section"}, "feedback_already_submitted": "You have already submitted feedback for this trip", "@feedback_already_submitted": {"description": "Message shown when feedback already exists"}, "submit_feedback": "Submit <PERSON>", "@submit_feedback": {"description": "Button label to submit feedback"}, "max_images_reached": "Maximum 5 images allowed", "@max_images_reached": {"description": "Error message when max images reached"}, "please_rate_trip": "Please rate your trip", "@please_rate_trip": {"description": "Error message when rating not selected"}, "feedback_submitted_success": "<PERSON><PERSON><PERSON> submitted successfully", "@feedback_submitted_success": {"description": "Success message after feedback submission"}, "app_feedback_title": "Application Feedback", "@app_feedback_title": {"description": "Title for application feedback screen"}, "app_feedback_description": "Help us improve the app by sharing your feedback, bug reports, or suggestions.", "@app_feedback_description": {"description": "Description for app feedback screen"}, "app_feedback_hint": "Describe the issue or suggestion in detail...", "@app_feedback_hint": {"description": "Placeholder for app feedback message field"}, "current_screen_screenshot": "Current Screen Screenshot", "@current_screen_screenshot": {"description": "Label for screenshot section"}, "include_screenshot": "Include screenshot", "@include_screenshot": {"description": "Checkbox label for including screenshot"}, "additional_images": "Additional Images", "@additional_images": {"description": "Label for additional images section"}, "max_5_images": "Maximum 5 images (5MB each)", "@max_5_images": {"description": "Helper text for image limits"}, "image_too_large": "Image size must be less than 5MB", "@image_too_large": {"description": "Error message for large images"}, "please_enter_message": "Please enter a message", "@please_enter_message": {"description": "Error when message is empty"}, "error_with_details": "Error: {details}", "@error_with_details": {"description": "Generic error message with details", "placeholders": {"details": {"type": "String", "example": "Network connection failed"}}}, "error_creating_chat": "Error creating chat: {error}", "@error_creating_chat": {"description": "Error message when chat creation fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "error_loading_feedback": "Error loading linked feedback: {error}", "@error_loading_feedback": {"description": "Error message when loading feedback fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "error_picking_image": "Error picking image: {error}", "@error_picking_image": {"description": "Error message when image picking fails", "placeholders": {"error": {"type": "String", "example": "Permission denied"}}}, "error_sending_image": "Error sending image: {error}", "@error_sending_image": {"description": "Error message when image sending fails", "placeholders": {"error": {"type": "String", "example": "Upload failed"}}}, "error_checking_feedback": "Error checking existing feedback: {error}", "@error_checking_feedback": {"description": "Error message when checking feedback fails", "placeholders": {"error": {"type": "String", "example": "Database error"}}}, "error_uploading_image": "Error uploading image: {error}", "@error_uploading_image": {"description": "Error message when image upload fails", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "error_submitting_feedback": "Error submitting feedback: {error}", "@error_submitting_feedback": {"description": "Error message when feedback submission fails", "placeholders": {"error": {"type": "String", "example": "Server error"}}}, "error_capturing_screenshot": "Error capturing screenshot: {error}", "@error_capturing_screenshot": {"description": "Error message when screenshot capture fails", "placeholders": {"error": {"type": "String", "example": "Permission denied"}}}, "settings": "Settings", "@settings": {"description": "Settings menu item"}, "notificationSettings": "Notification Settings", "@notificationSettings": {"description": "Notification settings screen title"}, "settings_general": "General", "@settings_general": {"description": "General settings section"}, "tripNotifications": "Trip Notifications", "@tripNotifications": {"description": "Trip notifications section"}, "ringtoneNotifications": "Ringtone Notifications", "@ringtoneNotifications": {"description": "Ringtone notifications setting"}, "ringtoneNotificationsDesc": "Play a ringtone for important notifications", "@ringtoneNotificationsDesc": {"description": "Ringtone notifications description"}, "mainPage_usingDefaultEnabled": "Using default (enabled)", "@mainPage_usingDefaultEnabled": {"description": "Shows when using admin default setting which is enabled in main page"}, "mainPage_usingDefaultDisabled": "Using default (disabled)", "@mainPage_usingDefaultDisabled": {"description": "Shows when using admin default setting which is disabled in main page"}, "mainPage_driverMoving": "Driver Moving", "@mainPage_driverMoving": {"description": "Driver moving notification setting in main page"}, "mainPage_driverMovingDesc": "Get notified when your driver starts heading to pick you up", "@mainPage_driverMovingDesc": {"description": "Driver moving notification description in main page"}, "mainPage_driverArrived": "Driver Arrived", "@mainPage_driverArrived": {"description": "Driver arrived notification setting in main page"}, "mainPage_driverArrivedDesc": "Get notified when your driver has arrived at pickup location", "@mainPage_driverArrivedDesc": {"description": "Driver arrived notification description in main page"}, "mainPage_paymentCompleted": "Payment Completed", "@mainPage_paymentCompleted": {"description": "Payment completed notification setting in main page"}, "mainPage_paymentCompletedDesc": "Get notified when your trip payment is processed", "@mainPage_paymentCompletedDesc": {"description": "Payment completed notification description in main page"}, "mainPage_reservations": "Reservations", "@mainPage_reservations": {"description": "Reservations section in main page"}, "mainPage_reservationReminders": "Reservation Reminders", "@mainPage_reservationReminders": {"description": "Reservation reminders setting in main page"}, "mainPage_reservationRemindersDesc": "Get reminders before your scheduled trips", "@mainPage_reservationRemindersDesc": {"description": "Reservation reminders description in main page"}, "mainPage_ringtonePermissionTitle": "Enable Ringtone Notifications?", "@mainPage_ringtonePermissionTitle": {"description": "Title for ringtone permission dialog in main page"}, "mainPage_ringtonePermissionMessage": "Would you like to receive important trip notifications with a ringtone?", "@mainPage_ringtonePermissionMessage": {"description": "Message for ringtone permission dialog in main page"}, "mainPage_ringtonePermissionDescription": "This includes notifications when your driver is on the way or has arrived.", "@mainPage_ringtonePermissionDescription": {"description": "Description for ringtone permission dialog in main page"}, "mainPage_noThanks": "No Thanks", "@mainPage_noThanks": {"description": "No thanks button in main page"}, "mainPage_enableRingtone": "Enable Ringtone", "@mainPage_enableRingtone": {"description": "Enable ringtone button in main page"}, "passengerCountSlider_title": "Number of Passengers", "@passengerCountSlider_title": {"description": "Title for the passenger count slider"}, "addVehicle_title": "Add Vehicle", "@addVehicle_title": {"description": "Title for the add vehicle screen"}, "no_documents_uploaded": "No documents uploaded yet", "@no_documents_uploaded": {"description": "Message when no documents are uploaded"}, "no_vehicles_added": "No vehicles added yet", "@no_vehicles_added": {"description": "Message when no vehicles are added"}, "vehicle_capacity_passengers": "Capacity: {maxPassengers} passengers", "@vehicle_capacity_passengers": {"description": "Shows the vehicle capacity in passengers", "placeholders": {"maxPassengers": {"type": "int", "example": "4"}}}, "vehicle_currentlyAssigned": "Currently Assigned", "@vehicle_currentlyAssigned": {"description": "Label for currently assigned vehicle badge"}, "vehicle_error": "Error: {error}", "@vehicle_error": {"description": "Error message for vehicle list", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "vehicle_assignedVehicle": "Assigned Vehicle", "@vehicle_assignedVehicle": {"description": "Section header for assigned vehicle"}, "vehicle_myVehicles": "My Vehicles", "@vehicle_myVehicles": {"description": "Section header for user's vehicles"}, "document_expiresInDays": "Expires in {days} days", "@document_expiresInDays": {"description": "Shows how many days until document expires", "placeholders": {"days": {"type": "int", "example": "5"}}}, "document_expiresOn": "Expires: {date}", "@document_expiresOn": {"description": "Shows the expiry date of a document", "placeholders": {"date": {"type": "String", "example": "Jun 10, 2024"}}}, "menuDrawer_welcome": "Welcome", "@menuDrawer_welcome": {"description": "Welcome message in the menu drawer"}, "mainPage_logout": "Logout", "@mainPage_logout": {"description": "Logout button in main page"}, "mainPage_menu": "<PERSON><PERSON>", "@mainPage_menu": {"description": "Menu label in main page"}, "mainPage_no": "No", "@mainPage_no": {"description": "No button in main page dialogs"}, "mainPage_yes": "Yes", "@mainPage_yes": {"description": "Yes button in main page dialogs"}, "mainPage_save": "Save", "@mainPage_save": {"description": "Save button in main page dialogs"}, "mainPage_cancel": "Cancel", "@mainPage_cancel": {"description": "Cancel button in main page dialogs"}, "mainPage_error": "Error", "@mainPage_error": {"description": "Error message in main page dialogs"}, "mainPage_settings": "Settings", "@mainPage_settings": {"description": "Settings menu item in main page"}, "mainPage_notificationSettings": "Notification Settings", "@mainPage_notificationSettings": {"description": "Notification settings screen title in main page"}, "mainPage_tripNotifications": "Trip Notifications", "@mainPage_tripNotifications": {"description": "Trip notifications section in main page"}, "mainPage_ringtoneNotifications": "Ringtone Notifications", "@mainPage_ringtoneNotifications": {"description": "Ringtone notifications setting in main page"}, "mainPage_ringtoneNotificationsDesc": "Play a ringtone for important notifications", "@mainPage_ringtoneNotificationsDesc": {"description": "Ringtone notifications description in main page"}, "notificationSettings_failedToLoadPreferences": "Failed to load preferences", "@notificationSettings_failedToLoadPreferences": {"description": "Error message when preferences fail to load"}, "notificationSettings_checkConnectionAndRetry": "Please check your connection and try again", "@notificationSettings_checkConnectionAndRetry": {"description": "Instructions when preferences fail to load"}, "notificationSettings_retry": "Retry", "@notificationSettings_retry": {"description": "Retry button label"}, "notificationSettings_loadingPreferences": "Loading preferences...", "@notificationSettings_loadingPreferences": {"description": "Loading message for preferences"}, "notificationSettings_general": "General", "@notificationSettings_general": {"description": "General settings section header"}, "notificationSettings_tripNotifications": "Trip Notifications", "@notificationSettings_tripNotifications": {"description": "Trip notifications section header"}, "notificationSettings_reservations": "Reservations", "@notificationSettings_reservations": {"description": "Reservations section header"}, "notificationSettings_driverMoving": "Driver Moving", "@notificationSettings_driverMoving": {"description": "Driver moving notification setting"}, "notificationSettings_driverMovingDesc": "Get notified when your driver starts heading to pick you up", "@notificationSettings_driverMovingDesc": {"description": "Driver moving notification description"}, "notificationSettings_driverArrived": "Driver Arrived", "@notificationSettings_driverArrived": {"description": "Driver arrived notification setting"}, "notificationSettings_driverArrivedDesc": "Get notified when your driver has arrived at pickup location", "@notificationSettings_driverArrivedDesc": {"description": "Driver arrived notification description"}, "notificationSettings_paymentCompleted": "Payment Completed", "@notificationSettings_paymentCompleted": {"description": "Payment completed notification setting"}, "notificationSettings_paymentCompletedDesc": "Get notified when your trip payment is processed", "@notificationSettings_paymentCompletedDesc": {"description": "Payment completed notification description"}, "notificationSettings_reservationReminders": "Reservation Reminders", "@notificationSettings_reservationReminders": {"description": "Reservation reminders setting"}, "notificationSettings_reservationRemindersDesc": "Get reminders before your scheduled trips", "@notificationSettings_reservationRemindersDesc": {"description": "Reservation reminders description"}, "driverDocuments_unableToOpenDocument": "Unable to open this document", "@driverDocuments_unableToOpenDocument": {"description": "Error message when unable to open document"}, "documentDetail_title": "Document Details", "@documentDetail_title": {"description": "Title for document detail screen"}, "documentDetail_notAvailable": "Document not available", "@documentDetail_notAvailable": {"description": "Message when document file is not available"}, "documentDetail_failedToLoadImage": "Failed to load image", "@documentDetail_failedToLoadImage": {"description": "Error message when image fails to load"}, "documentDetail_previewNotAvailable": "Document preview not available", "@documentDetail_previewNotAvailable": {"description": "Message when document preview is not available"}, "documentDetail_unsupportedFormat": "Unsupported file format", "@documentDetail_unsupportedFormat": {"description": "Message for unsupported file formats"}, "documentDetail_notFound": "We could not find this document", "@documentDetail_notFound": {"description": "Message when document is not found or cannot be loaded"}, "documentDetail_expired": "This document has expired", "@documentDetail_expired": {"description": "Message when document has expired"}, "documentDetail_documentName": "Document Name", "@documentDetail_documentName": {"description": "Label for document name field"}, "documentDetail_uploadDate": "Upload Date", "@documentDetail_uploadDate": {"description": "Label for upload date field"}, "documentDetail_expiryDate": "Expiry Date", "@documentDetail_expiryDate": {"description": "Label for expiry date field"}, "documentDetail_notes": "Notes", "@documentDetail_notes": {"description": "Label for notes field"}, "documentDetail_reviewInformation": "Review Information", "@documentDetail_reviewInformation": {"description": "Section header for review information"}, "documentDetail_reviewedDate": "Reviewed Date", "@documentDetail_reviewedDate": {"description": "Label for reviewed date field"}, "documentDetail_adminNotes": "Admin Notes", "@documentDetail_adminNotes": {"description": "Label for admin notes field"}, "documentDetail_documentPreview": "Document Preview", "@documentDetail_documentPreview": {"description": "Section header for document preview"}, "documentUpload_permissionDenied": "Permission Denied", "@documentUpload_permissionDenied": {"description": "Title for permission denied errors"}, "documentUpload_cameraPermissionRequired": "Camera permission is required to take photos", "@documentUpload_cameraPermissionRequired": {"description": "Message when camera permission is needed"}, "documentUpload_photoLibraryPermissionRequired": "Photo library permission is required to select photos", "@documentUpload_photoLibraryPermissionRequired": {"description": "Message when photo library permission is needed"}, "documentUpload_storagePermissionRequiredPhotos": "Storage permission is required to select photos", "@documentUpload_storagePermissionRequiredPhotos": {"description": "Message when storage permission is needed for photos"}, "documentUpload_storagePermissionRequiredFiles": "Storage permission is required to select files", "@documentUpload_storagePermissionRequiredFiles": {"description": "Message when storage permission is needed for files"}, "documentUpload_fileTooLarge": "File Too Large", "@documentUpload_fileTooLarge": {"description": "Title for file too large error"}, "documentUpload_fileSizeLimit": "Please select a file smaller than 5MB", "@documentUpload_fileSizeLimit": {"description": "Message about file size limit"}, "documentUpload_platformError": "Platform Error", "@documentUpload_platformError": {"description": "Title for platform errors"}, "documentUpload_deviceError": "Device error: {error}", "@documentUpload_deviceError": {"description": "Message for device errors", "placeholders": {"error": {"type": "String", "example": "Permission denied"}}}, "documentUpload_failedToPickFile": "Failed to pick file. Please try again.", "@documentUpload_failedToPickFile": {"description": "Error message when file picking fails"}, "documentUpload_cameraError": "Camera Error", "@documentUpload_cameraError": {"description": "Title for camera errors"}, "documentUpload_failedToCaptureImage": "Failed to capture image. Please try again.", "@documentUpload_failedToCaptureImage": {"description": "Error message when image capture fails"}, "documentUpload_noFileSelected": "No File Selected", "@documentUpload_noFileSelected": {"description": "Title when no file is selected"}, "documentUpload_selectDocumentPrompt": "Please select a document to upload", "@documentUpload_selectDocumentPrompt": {"description": "Message prompting to select a document"}, "documentUpload_noExpiryDate": "No Expiry Date", "@documentUpload_noExpiryDate": {"description": "Title when no expiry date is selected"}, "documentUpload_selectExpiryDatePrompt": "Please select an expiry date for the document", "@documentUpload_selectExpiryDatePrompt": {"description": "Message prompting to select expiry date"}, "documentUpload_uploadSuccess": "Document uploaded successfully", "@documentUpload_uploadSuccess": {"description": "Success message after document upload"}, "documentUpload_uploadFailed": "Upload Failed", "@documentUpload_uploadFailed": {"description": "Title for upload failure"}, "documentUpload_firebaseError": "Firebase error: {error}", "@documentUpload_firebaseError": {"description": "Firebase error message", "placeholders": {"error": {"type": "String", "example": "Network error"}}}, "documentUpload_unexpectedError": "An unexpected error occurred. Please try again.", "@documentUpload_unexpectedError": {"description": "Message for unexpected errors"}, "documentUpload_documentType": "Document Type", "@documentUpload_documentType": {"description": "Label for document type field"}, "documentUpload_documentName": "Document Name", "@documentUpload_documentName": {"description": "Label for document name field"}, "documentUpload_examplePrefix": "e.g., {example}", "@documentUpload_examplePrefix": {"description": "Example prefix for hints", "placeholders": {"example": {"type": "String", "example": "Driver's License"}}}, "documentUpload_enterDocumentName": "Please enter a document name", "@documentUpload_enterDocumentName": {"description": "Validation message for document name"}, "documentUpload_expiryDate": "Expiry Date", "@documentUpload_expiryDate": {"description": "Label for expiry date field"}, "documentUpload_selectExpiryDate": "Select expiry date", "@documentUpload_selectExpiryDate": {"description": "Placeholder for expiry date picker"}, "documentUpload_notesOptional": "Notes (Optional)", "@documentUpload_notesOptional": {"description": "Label for optional notes field"}, "documentUpload_additionalInfoHint": "Any additional information", "@documentUpload_additionalInfoHint": {"description": "Hint for notes field"}, "documentUpload_selectDocument": "Select Document", "@documentUpload_selectDocument": {"description": "Section header for document selection"}, "documentUpload_chooseFile": "Choose <PERSON>", "@documentUpload_chooseFile": {"description": "Button label to choose file"}, "documentUpload_takePhoto": "Take Photo", "@documentUpload_takePhoto": {"description": "Button label to take photo"}, "documentUpload_acceptedFormats": "Accepted formats: PDF, JPG, PNG (Max 5MB)", "@documentUpload_acceptedFormats": {"description": "Information about accepted file formats"}, "paymentDialog_cancel": "Cancel", "@paymentDialog_cancel": {"description": "Cancel button in payment dialog"}, "addVehicle_saveButton": "Save", "@addVehicle_saveButton": {"description": "Save button in add vehicle screen"}, "chatDialog_cancel": "Cancel", "@chatDialog_cancel": {"description": "Cancel button in chat dialog"}, "chat_yesterday": "Yesterday", "@chat_yesterday": {"description": "Yesterday text in chat screen"}, "chat_daysAgo": "days ago", "@chat_daysAgo": {"description": "Days ago text in chat screen"}, "chat_supportTeam": "Support Team", "@chat_supportTeam": {"description": "Support team label in chat screen"}, "chat_typeMessage": "Type a message...", "@chat_typeMessage": {"description": "Placeholder text for message input in chat screen"}, "feedback_camera": "Camera", "@feedback_camera": {"description": "Camera button in feedback screen"}, "feedback_gallery": "Gallery", "@feedback_gallery": {"description": "Gallery button in feedback screen"}, "tripDetails_locationUnknown": "Unknown", "@tripDetails_locationUnknown": {"description": "Text shown when location is unknown in trip details"}, "tripDetails_fixedPrice": "Carburant inclus", "@tripDetails_fixedPrice": {"description": "Fixed price label in trip details"}, "tripDetails_perHour": "Carburant en sus", "@tripDetails_perHour": {"description": "Per hour label in trip details"}, "driverDocuments_title": "My Documents", "@driverDocuments_title": {"description": "Title for driver documents screen"}, "documentUpload_uploadButton": "Upload Document", "@documentUpload_uploadButton": {"description": "Upload document button text"}, "vehicleManagement_title": "My Vehicles", "@vehicleManagement_title": {"description": "Title for vehicle management screen"}, "vehicleManagement_addButton": "Add Vehicle", "@vehicleManagement_addButton": {"description": "Add vehicle button text"}, "documentUpload_title": "Upload Document", "@documentUpload_title": {"description": "Title for document upload screen"}, "driverRating_newDriver": "New Driver", "@driverRating_newDriver": {"description": "Text displayed for drivers who have no ratings yet"}, "mapScreen_failedToGetPredictions": "Failed to get place predictions. Please check your Internet connection.", "@mapScreen_failedToGetPredictions": {"description": "Error message when place predictions fail to load"}, "mapScreen_failedToGetPlaceDetails": "Failed to get place details. Please check your Internet connection.", "@mapScreen_failedToGetPlaceDetails": {"description": "Error message when place details fail to load"}, "mapScreen_loadingMap": "Loading map...", "@mapScreen_loadingMap": {"description": "Loading status message for map initialization"}, "passengerTripControl_calculatingCost": "Calculating cost...", "@passengerTripControl_calculatingCost": {"description": "Message shown while trip cost is being calculated"}, "passengerTripControl_viewingTrip": "Viewing {status} trip", "@passengerTripControl_viewingTrip": {"description": "Message shown when viewing a non-active trip", "placeholders": {"status": {"type": "String", "example": "completed"}}}, "tripDetails_confirmDelete": "Confirm Delete", "@tripDetails_confirmDelete": {"description": "Title for delete confirmation dialog"}, "tripDetails_confirmDeleteMessage": "Are you sure you want to delete this trip?", "@tripDetails_confirmDeleteMessage": {"description": "Message for delete confirmation dialog"}, "tripDetails_cancelButton": "Cancel", "@tripDetails_cancelButton": {"description": "Cancel button text in delete confirmation dialog"}, "tripDetails_deleteButton": "Delete", "@tripDetails_deleteButton": {"description": "Delete button text in delete confirmation dialog"}, "navigationState_cannotShowTripError": "Cannot show trip while you have an active trip", "@navigationState_cannotShowTripError": {"description": "Error message when trying to view a trip while having an active trip"}}