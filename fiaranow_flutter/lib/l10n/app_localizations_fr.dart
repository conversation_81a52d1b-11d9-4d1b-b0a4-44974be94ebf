import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get mainPage_appTitle => 'Fiaranow';

  @override
  String get mainPage_choosePrimaryActivity => 'Choisissez votre activité principale';

  @override
  String get mainPage_rider => 'Passager';

  @override
  String get mainPage_driver => 'Conducteur';

  @override
  String get mainPage_changeAnytime => 'Vous pouvez le modifier à tout moment depuis les paramètres, pas d\'inquiétude.';

  @override
  String get menuDrawer_menu => 'Menu';

  @override
  String get menuDrawer_logout => 'Déconnexion';

  @override
  String get dialog_logoutConfirmation => 'Êtes-vous sûr de vouloir vous déconnecter ?';

  @override
  String get dialog_no => 'Non';

  @override
  String get dialog_yes => 'Oui';

  @override
  String get passengerCountDialog_confirm => 'Confirmer';

  @override
  String get mainPage_chooseLanguage => 'Choisir la langue';

  @override
  String get menuDrawer_theme => 'Thème';

  @override
  String get menuDrawer_themeSystem => 'Système';

  @override
  String get menuDrawer_themeLight => 'Clair';

  @override
  String get menuDrawer_themeDark => 'Sombre';

  @override
  String get mainPage_english => 'Anglais';

  @override
  String get mainPage_french => 'Français';

  @override
  String get mainPage_welcome => 'Bienvenue';

  @override
  String get notification_screenTitle => 'Activer les notifications';

  @override
  String get notification_enableButton => 'Activer les notifications';

  @override
  String get notification_description => 'En tant que conducteur, vous devez activer les notifications pour recevoir les demandes de course et les mises à jour importantes.';

  @override
  String get notification_requiredTitle => 'Notifications requises';

  @override
  String get notification_requiredContent => 'Les notifications sont requises pour le mode conducteur. Veuillez les activer dans les paramètres.';

  @override
  String get notification_cancel => 'Annuler';

  @override
  String get notification_openSettings => 'Ouvrir les paramètres';

  @override
  String get driverMode_permissionTitle => 'Permissions du mode conducteur';

  @override
  String get driverMode_permissionDescription => 'Pour opérer en tant que conducteur, nous avons besoin de votre permission pour envoyer des notifications et suivre votre position en arrière-plan.';

  @override
  String get driverMode_notificationPermission => 'Notifications pour les demandes de course';

  @override
  String get driverMode_backgroundLocationPermission => 'Localisation en arrière-plan (Toujours autoriser)';

  @override
  String get driverMode_grantPermissions => 'Accorder les permissions';

  @override
  String get driverMode_locationRequiredTitle => 'Permission de localisation requise';

  @override
  String get driverMode_locationRequiredContent => 'La permission de localisation est requise pour le mode conducteur. Veuillez l\'activer dans les paramètres.';

  @override
  String get driverMode_backgroundLocationTitle => 'Localisation en arrière-plan requise';

  @override
  String get driverMode_backgroundLocationExplanation => 'Le mode conducteur nécessite la permission de localisation \'Toujours autoriser\' pour suivre votre position et vous attribuer des courses même lorsque l\'application est en arrière-plan.';

  @override
  String get driverMode_backgroundLocationInstructions => 'Veuillez suivre ces étapes :';

  @override
  String get driverMode_iosStep1 => 'Appuyez sur \'Ouvrir les paramètres\' ci-dessous';

  @override
  String get driverMode_iosStep2 => 'Sélectionnez \'Localisation\' et choisissez \'Toujours\'';

  @override
  String get driverMode_iosStep3 => 'Retournez à l\'application et réessayez';

  @override
  String get driverMode_androidStep1 => 'Appuyez sur \'Ouvrir les paramètres\' ci-dessous';

  @override
  String get driverMode_androidStep2 => 'Sélectionnez \'Localisation\' et choisissez \'Autoriser tout le temps\'';

  @override
  String get driverMode_androidStep3 => 'Retournez à l\'application et réessayez';

  @override
  String get genericError => 'Une erreur s\'est produite. Veuillez réessayer.';

  @override
  String get driverProfileForm_screenTitle => 'Profil du conducteur';

  @override
  String get driverProfileForm_vehicleBrand => 'Marque du véhicule';

  @override
  String get driverProfileForm_vehicleModel => 'Modèle du véhicule';

  @override
  String get driverProfileForm_vehicleColor => 'Couleur du véhicule';

  @override
  String get driverProfileForm_vehicleYear => 'Année de fabrication';

  @override
  String get driverProfileForm_registrationNumber => 'Numéro d\'immatriculation';

  @override
  String get driverProfileForm_saveButton => 'Enregistrer';

  @override
  String get driverProfileForm_maxPassengers => 'Nombre maximum de passagers';

  @override
  String get mainPage_editDriverProfile => 'Modifier le profil du conducteur';

  @override
  String get mainPage_notADriver => 'Pas un conducteur';

  @override
  String get mainPage_setPrimaryActivityToDriver => 'Vous devez d\'abord définir votre activité principale sur Conducteur.';

  @override
  String get mainPage_profile => 'Profil';

  @override
  String get mainPage_maps => 'Carte';

  @override
  String get mainPage_history => 'Historique';

  @override
  String get mainPage_home => 'Accueil';

  @override
  String get mainPage_primaryActivity => 'Activité principale';

  @override
  String get mainPage_driverProfile => 'Profil conducteur';

  @override
  String get mapScreen_pickupAddressInputText => 'Adresse de départ';

  @override
  String get mapScreen_destinationAddressInputText => 'Adresse d\'arrivée';

  @override
  String get mapScreen_confirmPickupLocationButton => 'Confirmer le lieu de départ ?';

  @override
  String get mapScreen_confirmDestinationButton => 'Confirmer le lieu d\'arrivée ?';

  @override
  String get mapScreen_noDriversAvailable => 'Aucun conducteur disponible à proximité';

  @override
  String get mapScreen_availableDriversHeader => 'Conducteurs disponibles';

  @override
  String get mapScreen_selectDriverButton => 'Sélectionner';

  @override
  String get mapScreen_notEnoughSeats => 'Pas assez de places';

  @override
  String mapScreen_seatsAvailable(int maxPassengers) {
    return 'Places: $maxPassengers';
  }

  @override
  String mapScreen_seatsNeeded(int passengerCount) {
    return '(Besoin de $passengerCount)';
  }

  @override
  String get mapScreen_changeAddressesButton => 'Modifier l\'adresse';

  @override
  String get mapScreen_showNearbyDriversButton => 'Afficher les conducteurs à proximité';

  @override
  String get mapScreen_stopServiceButton => 'Arrêter le service';

  @override
  String get mapScreen_startServiceButton => 'Démarrer le service';

  @override
  String get mapScreen_checkInternetButton => 'Vérifier la connexion Internet';

  @override
  String get home_welcomeText => 'Bienvenue à Fiaranow';

  @override
  String get phoneNumberForm_screenTitle => 'Définir le numéro de téléphone';

  @override
  String get phoneNumberForm_phoneNumber => 'Numéro de téléphone';

  @override
  String get phoneNumberForm_saveButton => 'Enregistrer';

  @override
  String get mapScreen_driverProfileNeedsConfirmation => 'Le profil du conducteur doit être confirmé';

  @override
  String mapScreen_tripDistance(String distance) {
    return 'Distance du trajet: $distance km';
  }

  @override
  String mapScreen_estimatedDuration(String duration) {
    return 'Durée estimée: $duration';
  }

  @override
  String get mapScreen_noDriversAvailableNearby => 'Aucun conducteur disponible à proximité';

  @override
  String mapScreen_roadDistance(String distance) {
    return 'Distance routière: $distance km';
  }

  @override
  String mapScreen_approachingETA(String eta) {
    return 'ETA d\'approche: $eta minutes';
  }

  @override
  String get mapScreen_cancel => 'Annuler';

  @override
  String get mapScreen_tripRequests => 'Demandes de trajet';

  @override
  String get mapScreen_noTripRequests => 'Aucune demande de trajet pour le moment.';

  @override
  String mapScreen_from(String startLat, String startLon) {
    return 'De: $startLat, $startLon';
  }

  @override
  String mapScreen_to(String destLat, String destLon) {
    return 'À: $destLat, $destLon';
  }

  @override
  String mapScreen_tripDistanceLabel(String distance, String eta) {
    return 'Distance du trajet: $distance km, ETA: $eta minutes';
  }

  @override
  String mapScreen_approachingDistance(String distance, String eta) {
    return 'Distance d\'approche: $distance km, ETA: $eta minutes';
  }

  @override
  String get mapScreen_accept => 'Accepter';

  @override
  String get mapScreen_reject => 'Rejeter';

  @override
  String get mapScreen_confirmRejection => 'Confirmer le rejet';

  @override
  String get mapScreen_confirmRejectionMessage => 'Êtes-vous sûr de vouloir rejeter cette demande de trajet?';

  @override
  String get mapScreen_noActiveTrip => 'Aucun trajet actif.';

  @override
  String get mapScreen_tripControl => 'Contrôle du trajet';

  @override
  String get mapScreen_tripHeader => 'Trajet';

  @override
  String get mapScreen_youShouldNowBeDriving => 'Vous devriez maintenant conduire pour vous approcher du client...';

  @override
  String get mapScreen_iHaveArrived => 'Je suis arrivé (En attente du passager)';

  @override
  String get mapScreen_waitingForBothToStart => 'En attente de démarrage du trajet...';

  @override
  String get mapScreen_pleaseWaitForPassenger => 'Veuillez attendre que le passager démarre également le trajet.';

  @override
  String get mapScreen_tripInProgress => 'Le trajet est en cours.';

  @override
  String get mapScreen_completeTrip => 'Terminer le trajet';

  @override
  String get mapScreen_tripCompleted => 'Le trajet est terminé.';

  @override
  String get mapScreen_markAsPaid => 'Marquer comme payé';

  @override
  String get mapScreen_pleaseWaitForDriver => 'Veuillez attendre que le conducteur démarre également le trajet.';

  @override
  String get mapScreen_changeAddresses => 'Changer d\'adresse(s)';

  @override
  String get mapScreen_showNearbyDrivers => 'Conducteurs à proximité';

  @override
  String get home_choosePrimaryActivity => 'Choisissez votre activité principale';

  @override
  String get home_setPhoneNumber => 'Définir votre numéro de téléphone';

  @override
  String get home_allowPushNotifications => 'Autoriser les notifications push';

  @override
  String get home_allowLocationPermission => 'Autoriser la permission de localisation';

  @override
  String get home_getRideNow => 'Un trajet maintenant !';

  @override
  String get home_reserveRide => 'Réserver un trajet';

  @override
  String get home_specialOffer => 'Offre spéciale';

  @override
  String get home_reserveCarNoGas => '• Réservez une voiture pour toute la journée pour €25';

  @override
  String get home_reserveCarWithGas => '• Réservez pour toute la journée pour €75';

  @override
  String get tripDetails_title => 'Détails du trajet';

  @override
  String get tripDetails_pickupTime => 'Heure de récupération';

  @override
  String get tripDetails_distance => 'Distance';

  @override
  String get tripDetails_duration => 'Durée';

  @override
  String get tripDetails_cost => 'Coût';

  @override
  String get tripDetails_passenger => 'Passager';

  @override
  String get tripDetails_passengerCount => 'Nombre de passagers';

  @override
  String get tripDetails_status => 'Statut';

  @override
  String get tripDetails_createdAt => 'Créé le';

  @override
  String get tripDetails_completedAt => 'Terminé le';

  @override
  String get tripDetails_cancelledAt => 'Annulé le';

  @override
  String get tripDetails_startLocation => 'Lieu de départ';

  @override
  String get tripDetails_arrivalLocation => 'Lieu d\'arrivée';

  @override
  String get tripDetails_driverLocation => 'Lieu du conducteur';

  @override
  String get tripDetails_showOnMap => 'Afficher sur la carte';

  @override
  String get mapScreen_locationPermissionTitle => 'Autorisation de localisation';

  @override
  String get mapScreen_locationPermissionMessageDriver => 'Pour recevoir des courses, nous avons besoin d\'accéder à votre localisation';

  @override
  String get mapScreen_locationPermissionMessageRider => 'Pour trouver des chauffeurs à proximité, nous avons besoin d\'accéder à votre localisation';

  @override
  String get mapScreen_enableLocationButton => 'Activer la localisation';

  @override
  String get mapScreen_reserveThisTrip => 'Réserver ce trajet';

  @override
  String mapScreen_routeSelectionTitle(int count) {
    return 'Sélection d\'itinéraire ($count)';
  }

  @override
  String mapScreen_routeReserveTitle(int count) {
    return 'Réserver un itinéraire ($count)';
  }

  @override
  String get mapScreen_reservationDateLabel => 'Date';

  @override
  String get mapScreen_reservationTimeLabel => 'Heure';

  @override
  String get mapScreen_confirmReservation => 'Confirmer la réservation';

  @override
  String get mapScreen_estimatedTripCost => 'Coût estimé du trajet : ';

  @override
  String get mapScreen_finalTripCost => 'Coût final du trajet :';

  @override
  String get mapScreen_currentTripCost => 'Coût estimé du trajet :';

  @override
  String get mapScreen_sorryTripCancelled => 'Désolé, ce trajet a été annulé.';

  @override
  String get mapScreen_driverAssigned => 'Un chauffeur vous a été attribué.';

  @override
  String get mapScreen_driverWillBeAssigned => 'Un chauffeur vous sera attribué. Veuillez patienter.';

  @override
  String get mapScreen_driverApproaching => 'Le chauffeur s\'approche de vous...';

  @override
  String get mapScreen_waitingTime => 'Temps d\'attente :';

  @override
  String get mapScreen_startTrip => 'Démarrer le trajet';

  @override
  String get mapScreen_enjoyTheRide => 'Profitez du trajet!';

  @override
  String get mapScreen_thankYouForRiding => 'Merci d\'avoir utilisé Fiaranow!';

  @override
  String get mapScreen_tripPaid => 'Le trajet est payé.';

  @override
  String mapScreen_reserveRoute(int count) {
    return 'Réserver un itinéraire ($count)';
  }

  @override
  String mapScreen_selectRoute(int count) {
    return 'Sélectionner un itinéraire ($count)';
  }

  @override
  String get mapScreen_routeWarningMessage => 'Pendant le trajet, le chauffeur pourrait choisir un itinéraire différent en fonction des conditions routières, du trafic ou à votre demande. Le coût du trajet sera mis à jour en conséquence.';

  @override
  String mapScreen_route(int number) {
    return 'Itinéraire $number';
  }

  @override
  String mapScreen_routeDetails(String duration, String distance, String cost) {
    return 'Durée : $duration, Distance : $distance km, Coût : $cost Ar';
  }

  @override
  String get mapScreen_success => 'Succès';

  @override
  String get mapScreen_tripReservedSuccessfully => 'Trajet réservé avec succès.';

  @override
  String get mapScreen_error => 'Erreur';

  @override
  String get mapScreen_driverAlreadyAssigned => 'Vous êtes déjà assigné à un autre trajet. Veuillez terminer ou annuler votre trajet actuel d\'abord.';

  @override
  String get mapScreen_driverNotAvailable => 'Vous ne remplissez pas les conditions de disponibilité. Veuillez vérifier vos documents et le statut de votre véhicule.';

  @override
  String get mapScreen_tripNotFound => 'Ce trajet n\'est plus disponible.';

  @override
  String get mapScreen_locationNotAvailable => 'Votre position n\'est pas disponible. Veuillez activer les services de localisation et réessayer.';

  @override
  String get mapScreen_selectFutureTime => 'Veuillez sélectionner une heure au moins 15 minutes dans le futur.';

  @override
  String get exit_dialog_title => 'Quitter l\'application';

  @override
  String get exit_dialog_message => 'Voulez-vous vraiment quitter l\'application ?';

  @override
  String get mapScreen_choosePaymentMethod => 'Choisir un mode de paiement';

  @override
  String get mapScreen_cashPayment => 'Paiement en espèces';

  @override
  String get mapScreen_mobileMoneyPayment => 'Paiement par Mobile Money';

  @override
  String get mapScreen_chooseRideType => 'Choisir le type de trajet';

  @override
  String get mapScreen_reserveRide => 'Réserver un trajet';

  @override
  String get mapScreen_chooseDateAndTime => 'Choisir la date et l\'heure';

  @override
  String get mapScreen_rideNow => 'Voyager maintenant';

  @override
  String get mapScreen_findAvailableDrivers => 'Trouver des chauffeurs disponibles';

  @override
  String get mapScreen_deviceClockInaccurate => 'L\'horloge de votre appareil est inexacte';

  @override
  String get mapScreen_adjustDeviceTimeSettings => 'Veuillez régler les paramètres d\'heure de votre appareil sur Automatique pour continuer à utiliser l\'application.';

  @override
  String mapScreen_currentTimeDifference(String seconds) {
    return 'Différence de temps actuelle : $seconds secondes';
  }

  @override
  String mapScreen_deviceClockOff(String seconds) {
    return 'L\'horloge de votre appareil est décalée de $seconds secondes. Pensez à activer les paramètres d\'heure automatiques.';
  }

  @override
  String get mapScreen_dismissClockWarning => 'Fermer';

  @override
  String get mapScreen_failedToGetRoutes => 'Impossible d\'obtenir les itinéraires. Veuillez vérifier votre connexion Internet.';

  @override
  String get serviceStatusUpdate_startService => 'Démarrer le service';

  @override
  String get serviceStatusUpdate_stopService => 'Arrêter le service';

  @override
  String get serviceStatusUpdate_whyStarting => 'Pourquoi démarrez-vous votre service ?';

  @override
  String get serviceStatusUpdate_whyStopping => 'Pourquoi arrêtez-vous votre service ?';

  @override
  String get serviceStatusUpdate_reason => 'Raison';

  @override
  String get serviceStatusUpdate_specifyReason => 'Veuillez spécifier votre raison';

  @override
  String get serviceStatusReason_morningServiceStart => 'Démarrage du service le matin';

  @override
  String get serviceStatusReason_eveningServiceStart => 'Démarrage du service le soir';

  @override
  String get serviceStatusReason_lunchBreak => 'Pause déjeuner';

  @override
  String get serviceStatusReason_prayerBreak => 'Pause prière';

  @override
  String get serviceStatusReason_fuelRefill => 'Ravitaillement en carburant';

  @override
  String get serviceStatusReason_vehicleMaintenance => 'Entretien du véhicule';

  @override
  String get serviceStatusReason_endOfShift => 'Fin de service';

  @override
  String get serviceStatusReason_emergencyStop => 'Arrêt d\'urgence';

  @override
  String get serviceStatusReason_switchActivity => 'Changement d\'activité';

  @override
  String get serviceStatusReason_appRelaunch => 'Relance de l\'application';

  @override
  String get serviceStatusReason_custom => 'Personnalisé';

  @override
  String get tripRejectionScreen_title => 'Refus de course';

  @override
  String get tripRejectionScreen_selectReason => 'Veuillez sélectionner une raison pour refuser cette course :';

  @override
  String get tripRejectionScreen_vehicleMalfunction => 'Problème de véhicule';

  @override
  String get tripRejectionScreen_tooFarPickup => 'Point de départ trop éloigné';

  @override
  String get tripRejectionScreen_heavyTraffic => 'Trafic dense dans la zone';

  @override
  String get tripRejectionScreen_unsafeArea => 'Zone non sécurisée';

  @override
  String get tripRejectionScreen_endingShiftSoon => 'Fin de service proche';

  @override
  String get tripRejectionScreen_vehicleCleaning => 'Nettoyage du véhicule nécessaire';

  @override
  String get tripRejectionScreen_passengerCapacityFull => 'Capacité passagers atteinte';

  @override
  String get tripRejectionScreen_batteryLow => 'Batterie faible';

  @override
  String get tripRejectionScreen_weatherConditions => 'Mauvaises conditions météo';

  @override
  String get tripRejectionScreen_custom => 'Autre raison (à préciser)';

  @override
  String get tripRejectionScreen_customReasonLabel => 'Veuillez préciser votre raison';

  @override
  String get tripRejectionScreen_confirm => 'Confirmer le refus';

  @override
  String get tripRejectionScreen_error => 'Erreur';

  @override
  String get tripRejectionScreen_pleaseEnterReason => 'Veuillez saisir votre raison de refus';

  @override
  String get mapScreen_cancelRequestFailed => 'Échec de l\'annulation de la demande. Veuillez réessayer.';

  @override
  String mapScreen_pickupAndTripDistance(String distance) {
    return 'Distance de récupération et trajet : $distance km';
  }

  @override
  String mapScreen_pickupDistance(String distance) {
    return 'Distance de récupération : $distance km';
  }

  @override
  String get mapScreen_pickupLocation => 'Lieu de récupération';

  @override
  String get mapScreen_pickupTime => 'Heure de récupération';

  @override
  String get mapScreen_destinationLocation => 'Lieu d\'arrivée';

  @override
  String get menuDrawer_profileSettings => 'Paramètres du profil';

  @override
  String get menuDrawer_currentMode => 'Mode actuel';

  @override
  String get menuDrawer_phoneNumber => 'Numéro de téléphone';

  @override
  String get menuDrawer_permissions => 'Autorisations';

  @override
  String get menuDrawer_pushNotifications => 'Notifications push';

  @override
  String get menuDrawer_gpsLocation => 'Localisation GPS';

  @override
  String get menuDrawer_gpsLocationBackground => 'Localisation GPS (Arrière-plan)';

  @override
  String get menuDrawer_locationWhileInUseDriverSubtitle => 'Mettre sur \'Toujours\' pour une expérience conducteur optimale.';

  @override
  String get menuDrawer_driverDocuments => 'Mes documents';

  @override
  String get menuDrawer_driverDocumentsDesc => 'Télécharger et gérer vos documents';

  @override
  String get updateRequiredScreen_message => 'Une mise à jour est requise pour continuer à utiliser l\'application.';

  @override
  String get updateRequiredScreen_updateNow => 'Mettre à jour maintenant';

  @override
  String get mapScreen_fullDayReservation => 'Réservation journée';

  @override
  String get mapScreen_fullDayReservationPrompt => 'Une réservation journée entière vous permet d\'avoir un chauffeur à votre disposition toute la journée. Le chauffeur vous prendra à l\'heure et à l\'endroit spécifiés, et sera disponible pour vous conduire où vous voulez tout au long de la journée.';

  @override
  String get mapScreen_fullDayPriceOptions => 'Choisissez l\'option de prix';

  @override
  String get mapScreen_fullDayFixedPrice => '75€ (carburant inclus)';

  @override
  String get mapScreen_gasIncluded => 'Carburant inclus';

  @override
  String get mapScreen_fullDayGasExcluded => '25€ (carburant en sus)';

  @override
  String get mapScreen_gasNotIncluded => 'Carburant en sus';

  @override
  String get mapScreen_fullDayReservationSuccess => 'Réservation journée entière créée avec succès !';

  @override
  String get auth_accountInUseTitle => 'Compte déjà utilisé';

  @override
  String get auth_accountInUseMessage => 'Votre compte est déjà connecté sur un autre appareil. Voulez-vous vous déconnecter des autres appareils ?';

  @override
  String get auth_logoutOtherDevices => 'Oui';

  @override
  String get auth_cancelLogin => 'Non';

  @override
  String get auth_forcedLogoutTitle => 'Déconnecté';

  @override
  String get auth_forcedLogoutMessage => 'Votre compte a été connecté sur un autre appareil.';

  @override
  String get auth_forcedLogoutButton => 'OK';

  @override
  String get tripStatus_preparing => 'En préparation';

  @override
  String get tripStatus_requestingDriver => 'Recherche de chauffeur';

  @override
  String get tripStatus_reserved => 'Réservé';

  @override
  String get tripStatus_driverApproaching => 'Chauffeur en approche';

  @override
  String get tripStatus_driverAwaiting => 'Chauffeur en attente';

  @override
  String get tripStatus_inProgress => 'En cours';

  @override
  String get tripStatus_completed => 'Terminé';

  @override
  String get tripStatus_cancelled => 'Annulé';

  @override
  String get tripStatus_paid => 'Payé';

  @override
  String get tripDetails_pricingOption => 'Option de prix';

  @override
  String get tripDetails_priceDetails => 'Détails du prix';

  @override
  String get tripDetails_locationDetails => 'Détails de l\'emplacement';

  @override
  String get tripDetails_unknownDestination => 'Destination inconnue';

  @override
  String get tripDetails_deleteTrip => 'Supprimer le trajet';

  @override
  String get history_noTripsYet => 'Aucun trajet pour le moment';

  @override
  String get history_title => 'Historique';

  @override
  String get home_welcome => 'Bienvenue';

  @override
  String get mapScreen_enjoyFullDayRide => 'Profitez de votre journée !';

  @override
  String get mapScreen_locationFallback => 'Lieu';

  @override
  String get mapScreen_destinationFallback => 'Destination';

  @override
  String get auth_networkErrorTitle => 'Erreur réseau';

  @override
  String get auth_networkErrorMessage => 'Veuillez vérifier votre connexion internet et réessayer.';

  @override
  String get navigationState_error => 'Erreur';

  @override
  String get navigationState_driverTripRequestsError => 'Erreur lors de l\'écoute des demandes de trajets du conducteur. Veuillez redémarrer l\'application.';

  @override
  String get navigationState_locationError => 'Erreur de localisation';

  @override
  String get navigationState_locationTrackingError => 'Impossible de suivre votre position. Veuillez vérifier vos paramètres de localisation et redémarrer l\'application.';

  @override
  String get appState_connectionStatusTitle => 'État de la connexion';

  @override
  String get appState_connectionRestored => 'Vous êtes connecté à Internet.';

  @override
  String get appState_connectionLost => 'Votre connexion Internet ne fonctionne pas bien en ce moment.';

  @override
  String get foregroundService_channelName => 'Service de trajet';

  @override
  String get foregroundService_channelDescription => 'Maintient l\'application active pendant un trajet en cours.';

  @override
  String get foregroundService_tripInProgress => 'Trajet en cours';

  @override
  String get foregroundService_tripOngoing => 'Votre trajet est en cours. Appuyez pour revenir à l\'application.';

  @override
  String get menuDrawer_notificationPermanentlyDenied => 'Refusé par l\'utilisateur';

  @override
  String get tripActionButton_error => 'Erreur';

  @override
  String get tripActionButton_cancel => 'Annuler';

  @override
  String get tripActionButton_start => 'Démarrer';

  @override
  String get tripActionButton_complete => 'Terminer';

  @override
  String get mainPage_chat => 'Aide';

  @override
  String get chat_title => 'Assistance par chat';

  @override
  String get chat_new => 'Nouvelle conversation';

  @override
  String get chat_no_conversations => 'Aucune conversation';

  @override
  String get chat_start_conversation_hint => 'Démarrez une nouvelle conversation pour obtenir de l\'aide';

  @override
  String get chat_new_dialog_title => 'Démarrer une nouvelle conversation';

  @override
  String get chat_subject => 'Sujet';

  @override
  String get chat_category => 'Catégorie';

  @override
  String get chat_start => 'Démarrer la discussion';

  @override
  String get chat_category_general => 'Assistance générale';

  @override
  String get chat_category_trip => 'Assistance trajet';

  @override
  String get chat_category_payment => 'Assistance paiement';

  @override
  String get chat_category_technical => 'Assistance technique';

  @override
  String get chat_category_feedback => 'Suivi des commentaires';

  @override
  String get chat_trip_button => 'Trajet';

  @override
  String get related_feedback => 'Commentaires associés';

  @override
  String get trip_feedback_title => 'Évaluation du trajet';

  @override
  String get trip_feedback_button => 'Donner un avis';

  @override
  String get trip_details => 'Détails du trajet';

  @override
  String get rate_your_trip => 'Évaluez votre trajet';

  @override
  String get feedback_message => 'Message de commentaire';

  @override
  String get feedback_message_hint => 'Parlez-nous de votre expérience...';

  @override
  String get add_photos => 'Ajouter des photos (facultatif)';

  @override
  String get feedback_already_submitted => 'Vous avez déjà envoyé un commentaire pour ce trajet';

  @override
  String get submit_feedback => 'Envoyer le commentaire';

  @override
  String get max_images_reached => 'Maximum 5 images autorisées';

  @override
  String get please_rate_trip => 'Veuillez évaluer votre trajet';

  @override
  String get feedback_submitted_success => 'Commentaire envoyé avec succès';

  @override
  String get app_feedback_title => 'Commentaires sur l\'application';

  @override
  String get app_feedback_description => 'Aidez-nous à améliorer l\'application en partageant vos commentaires, rapports de bugs ou suggestions.';

  @override
  String get app_feedback_hint => 'Décrivez le problème ou la suggestion en détail...';

  @override
  String get current_screen_screenshot => 'Capture d\'écran actuelle';

  @override
  String get include_screenshot => 'Inclure la capture d\'écran';

  @override
  String get additional_images => 'Images supplémentaires';

  @override
  String get max_5_images => 'Maximum 5 images (5 Mo chacune)';

  @override
  String get image_too_large => 'La taille de l\'image doit être inférieure à 5 Mo';

  @override
  String get please_enter_message => 'Veuillez entrer un message';

  @override
  String error_with_details(String details) {
    return 'Erreur : $details';
  }

  @override
  String error_creating_chat(String error) {
    return 'Erreur lors de la création de la discussion : $error';
  }

  @override
  String error_loading_feedback(String error) {
    return 'Erreur lors du chargement des commentaires associés : $error';
  }

  @override
  String error_picking_image(String error) {
    return 'Erreur lors de la sélection de l\'image : $error';
  }

  @override
  String error_sending_image(String error) {
    return 'Erreur lors de l\'envoi de l\'image : $error';
  }

  @override
  String error_checking_feedback(String error) {
    return 'Erreur lors de la vérification des commentaires existants : $error';
  }

  @override
  String error_uploading_image(String error) {
    return 'Erreur lors du téléchargement de l\'image : $error';
  }

  @override
  String error_submitting_feedback(String error) {
    return 'Erreur lors de l\'envoi du commentaire : $error';
  }

  @override
  String error_capturing_screenshot(String error) {
    return 'Erreur lors de la capture d\'écran : $error';
  }

  @override
  String get settings => 'Paramètres';

  @override
  String get notificationSettings => 'Paramètres de notification';

  @override
  String get settings_general => 'Général';

  @override
  String get tripNotifications => 'Notifications de trajet';

  @override
  String get ringtoneNotifications => 'Notifications avec sonnerie';

  @override
  String get ringtoneNotificationsDesc => 'Jouer une sonnerie pour les notifications importantes';

  @override
  String get mainPage_usingDefaultEnabled => 'Utilise le défaut (activé)';

  @override
  String get mainPage_usingDefaultDisabled => 'Utilise le défaut (désactivé)';

  @override
  String get mainPage_driverMoving => 'Chauffeur en mouvement';

  @override
  String get mainPage_driverMovingDesc => 'Être notifié lorsque votre chauffeur commence à se diriger vers vous';

  @override
  String get mainPage_driverArrived => 'Chauffeur arrivé';

  @override
  String get mainPage_driverArrivedDesc => 'Être notifié lorsque votre chauffeur est arrivé au lieu de récupération';

  @override
  String get mainPage_paymentCompleted => 'Paiement terminé';

  @override
  String get mainPage_paymentCompletedDesc => 'Être notifié lorsque le paiement de votre trajet est traité';

  @override
  String get mainPage_reservations => 'Réservations';

  @override
  String get mainPage_reservationReminders => 'Rappels de réservation';

  @override
  String get mainPage_reservationRemindersDesc => 'Recevoir des rappels avant vos trajets programmés';

  @override
  String get mainPage_ringtonePermissionTitle => 'Activer les notifications avec sonnerie?';

  @override
  String get mainPage_ringtonePermissionMessage => 'Souhaitez-vous recevoir des notifications importantes de trajet avec une sonnerie?';

  @override
  String get mainPage_ringtonePermissionDescription => 'Cela inclut les notifications lorsque votre chauffeur est en route ou est arrivé.';

  @override
  String get mainPage_noThanks => 'Non Merci';

  @override
  String get mainPage_enableRingtone => 'Activer la Sonnerie';

  @override
  String get passengerCountSlider_title => 'Nombre de passagers';

  @override
  String get addVehicle_title => 'Ajouter un véhicule';

  @override
  String get no_documents_uploaded => 'Aucun document téléchargé';

  @override
  String get no_vehicles_added => 'Aucun véhicule ajouté';

  @override
  String vehicle_capacity_passengers(int maxPassengers) {
    return 'Capacité : $maxPassengers passagers';
  }

  @override
  String get vehicle_currentlyAssigned => 'Actuellement assigné';

  @override
  String vehicle_error(String error) {
    return 'Erreur : $error';
  }

  @override
  String get vehicle_assignedVehicle => 'Véhicule assigné';

  @override
  String get vehicle_myVehicles => 'Mes véhicules';

  @override
  String document_expiresInDays(int days) {
    return 'Expire dans $days jours';
  }

  @override
  String document_expiresOn(String date) {
    return 'Expire le : $date';
  }

  @override
  String get menuDrawer_welcome => 'Bienvenue';

  @override
  String get mainPage_logout => 'Déconnexion';

  @override
  String get mainPage_menu => 'Menu';

  @override
  String get mainPage_no => 'Non';

  @override
  String get mainPage_yes => 'Oui';

  @override
  String get mainPage_save => 'Enregistrer';

  @override
  String get mainPage_cancel => 'Annuler';

  @override
  String get mainPage_error => 'Erreur';

  @override
  String get mainPage_settings => 'Paramètres';

  @override
  String get mainPage_notificationSettings => 'Paramètres de notification';

  @override
  String get mainPage_tripNotifications => 'Notifications de trajet';

  @override
  String get mainPage_ringtoneNotifications => 'Notifications avec sonnerie';

  @override
  String get mainPage_ringtoneNotificationsDesc => 'Jouer une sonnerie pour les notifications importantes';

  @override
  String get notificationSettings_failedToLoadPreferences => 'Échec du chargement des préférences';

  @override
  String get notificationSettings_checkConnectionAndRetry => 'Veuillez vérifier votre connexion et réessayer';

  @override
  String get notificationSettings_retry => 'Réessayer';

  @override
  String get notificationSettings_loadingPreferences => 'Chargement des préférences...';

  @override
  String get notificationSettings_general => 'Général';

  @override
  String get notificationSettings_tripNotifications => 'Notifications de trajet';

  @override
  String get notificationSettings_reservations => 'Réservations';

  @override
  String get notificationSettings_driverMoving => 'Chauffeur en mouvement';

  @override
  String get notificationSettings_driverMovingDesc => 'Être notifié lorsque votre chauffeur commence à se diriger vers vous';

  @override
  String get notificationSettings_driverArrived => 'Chauffeur arrivé';

  @override
  String get notificationSettings_driverArrivedDesc => 'Être notifié lorsque votre chauffeur est arrivé au lieu de récupération';

  @override
  String get notificationSettings_paymentCompleted => 'Paiement terminé';

  @override
  String get notificationSettings_paymentCompletedDesc => 'Être notifié lorsque le paiement de votre trajet est traité';

  @override
  String get notificationSettings_reservationReminders => 'Rappels de réservation';

  @override
  String get notificationSettings_reservationRemindersDesc => 'Recevoir des rappels avant vos trajets programmés';

  @override
  String get driverDocuments_unableToOpenDocument => 'Impossible d\'ouvrir ce document';

  @override
  String get documentDetail_title => 'Détails du document';

  @override
  String get documentDetail_notAvailable => 'Document non disponible';

  @override
  String get documentDetail_failedToLoadImage => 'Échec du chargement de l\'image';

  @override
  String get documentDetail_previewNotAvailable => 'Aperçu du document non disponible';

  @override
  String get documentDetail_unsupportedFormat => 'Format de fichier non pris en charge';

  @override
  String get documentDetail_notFound => 'Nous n\'avons pas pu trouver ce document';

  @override
  String get documentDetail_expired => 'Ce document a expiré';

  @override
  String get documentDetail_documentName => 'Nom du document';

  @override
  String get documentDetail_uploadDate => 'Date de téléchargement';

  @override
  String get documentDetail_expiryDate => 'Date d\'expiration';

  @override
  String get documentDetail_notes => 'Notes';

  @override
  String get documentDetail_reviewInformation => 'Informations de révision';

  @override
  String get documentDetail_reviewedDate => 'Date de révision';

  @override
  String get documentDetail_adminNotes => 'Notes de l\'administrateur';

  @override
  String get documentDetail_documentPreview => 'Aperçu du document';

  @override
  String get documentUpload_permissionDenied => 'Permission refusée';

  @override
  String get documentUpload_cameraPermissionRequired => 'L\'autorisation de l\'appareil photo est nécessaire pour prendre des photos';

  @override
  String get documentUpload_photoLibraryPermissionRequired => 'L\'autorisation de la bibliothèque photo est nécessaire pour sélectionner des photos';

  @override
  String get documentUpload_storagePermissionRequiredPhotos => 'L\'autorisation de stockage est nécessaire pour sélectionner des photos';

  @override
  String get documentUpload_storagePermissionRequiredFiles => 'L\'autorisation de stockage est nécessaire pour sélectionner des fichiers';

  @override
  String get documentUpload_fileTooLarge => 'Fichier trop volumineux';

  @override
  String get documentUpload_fileSizeLimit => 'Veuillez sélectionner un fichier inférieur à 5 Mo';

  @override
  String get documentUpload_platformError => 'Erreur de plateforme';

  @override
  String documentUpload_deviceError(String error) {
    return 'Erreur de l\'appareil : $error';
  }

  @override
  String get documentUpload_failedToPickFile => 'Échec de la sélection du fichier. Veuillez réessayer.';

  @override
  String get documentUpload_cameraError => 'Erreur de l\'appareil photo';

  @override
  String get documentUpload_failedToCaptureImage => 'Échec de la capture de l\'image. Veuillez réessayer.';

  @override
  String get documentUpload_noFileSelected => 'Aucun fichier sélectionné';

  @override
  String get documentUpload_selectDocumentPrompt => 'Veuillez sélectionner un document à télécharger';

  @override
  String get documentUpload_noExpiryDate => 'Aucune date d\'expiration';

  @override
  String get documentUpload_selectExpiryDatePrompt => 'Veuillez sélectionner une date d\'expiration pour le document';

  @override
  String get documentUpload_uploadSuccess => 'Document téléchargé avec succès';

  @override
  String get documentUpload_uploadFailed => 'Échec du téléchargement';

  @override
  String documentUpload_firebaseError(String error) {
    return 'Erreur Firebase : $error';
  }

  @override
  String get documentUpload_unexpectedError => 'Une erreur inattendue s\'est produite. Veuillez réessayer.';

  @override
  String get documentUpload_documentType => 'Type de document';

  @override
  String get documentUpload_documentName => 'Nom du document';

  @override
  String documentUpload_examplePrefix(String example) {
    return 'ex. : $example';
  }

  @override
  String get documentUpload_enterDocumentName => 'Veuillez entrer un nom de document';

  @override
  String get documentUpload_expiryDate => 'Date d\'expiration';

  @override
  String get documentUpload_selectExpiryDate => 'Sélectionner la date d\'expiration';

  @override
  String get documentUpload_notesOptional => 'Notes (facultatif)';

  @override
  String get documentUpload_additionalInfoHint => 'Toute information supplémentaire';

  @override
  String get documentUpload_selectDocument => 'Sélectionner un document';

  @override
  String get documentUpload_chooseFile => 'Choisir un fichier';

  @override
  String get documentUpload_takePhoto => 'Prendre une photo';

  @override
  String get documentUpload_acceptedFormats => 'Formats acceptés : PDF, JPG, PNG (Max 5 Mo)';

  @override
  String get paymentDialog_cancel => 'Annuler';

  @override
  String get addVehicle_saveButton => 'Enregistrer';

  @override
  String get chatDialog_cancel => 'Annuler';

  @override
  String get chat_yesterday => 'Hier';

  @override
  String get chat_daysAgo => 'jours';

  @override
  String get chat_supportTeam => 'Équipe d\'assistance';

  @override
  String get chat_typeMessage => 'Tapez un message...';

  @override
  String get feedback_camera => 'Appareil photo';

  @override
  String get feedback_gallery => 'Galerie';

  @override
  String get tripDetails_locationUnknown => 'Inconnu';

  @override
  String get tripDetails_fixedPrice => 'Carburant inclus';

  @override
  String get tripDetails_perHour => 'Carburant en sus';

  @override
  String get driverDocuments_title => 'Mes documents';

  @override
  String get documentUpload_uploadButton => 'Télécharger un document';

  @override
  String get vehicleManagement_title => 'Mes véhicules';

  @override
  String get vehicleManagement_addButton => 'Ajouter un véhicule';

  @override
  String get documentUpload_title => 'Télécharger un document';

  @override
  String get driverRating_newDriver => 'Nouveau Conducteur';

  @override
  String get mapScreen_failedToGetPredictions => 'Impossible d\'obtenir les suggestions d\'adresse. Veuillez vérifier votre connexion Internet.';

  @override
  String get mapScreen_failedToGetPlaceDetails => 'Impossible d\'obtenir les détails du lieu. Veuillez vérifier votre connexion Internet.';

  @override
  String get mapScreen_loadingMap => 'Chargement de la carte...';

  @override
  String get passengerTripControl_calculatingCost => 'Calcul du coût...';

  @override
  String passengerTripControl_viewingTrip(String status) {
    return 'Consultation du trajet $status';
  }

  @override
  String get tripDetails_confirmDelete => 'Confirmer la suppression';

  @override
  String get tripDetails_confirmDeleteMessage => 'Êtes-vous sûr de vouloir supprimer ce trajet ?';

  @override
  String get tripDetails_cancelButton => 'Annuler';

  @override
  String get tripDetails_deleteButton => 'Supprimer';

  @override
  String get navigationState_cannotShowTripError => 'Impossible d\'afficher le trajet pendant qu\'un trajet est actif';
}
