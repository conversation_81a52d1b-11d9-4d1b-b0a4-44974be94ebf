import { db, admin } from "./config";
import { sendPushNotification } from "./notification_utils";
import { logger } from "firebase-functions/v2";

// Notification priority and configuration types
interface NotificationPriority {
  android: 'normal' | 'high';
  ios: '5' | '10'; // APNs priority
}

interface NotificationConfig {
  title: string;
  body: string;
  data?: Record<string, string>;
  priority: NotificationPriority;
  channelId?: string;
  sound?: string;
  timeoutMs?: number;
}

// High-priority notification types for transport app
const HIGH_PRIORITY_TYPES = [
  'trip_request',
  'trip_cancelled', 
  'driver_moving',
  'driver_arrived',
  'chat_message',
  'emergency',
  'payment_required',
] as const;

/**
 * Create high-priority message for FCM
 */
function createHighPriorityMessage(
  tokens: string | string[],
  config: NotificationConfig
): admin.messaging.MulticastMessage | admin.messaging.Message {
  const baseMessage = {
    notification: {
      title: config.title,
      body: config.body,
    },
    data: {
      ...config.data,
      notificationId: Date.now().toString(),
      timestamp: new Date().toISOString(),
    },
    android: {
      priority: config.priority.android as 'high' | 'normal',
      notification: {
        channelId: config.channelId || 'default_channel',
        sound: config.sound || 'default',
        priority: config.priority.android === 'high' ? 'max' : 'default',
        ...(config.timeoutMs && { timeoutAfter: config.timeoutMs }),
      },
    },
    apns: {
      headers: {
        'apns-priority': config.priority.ios,
        'apns-push-type': 'alert',
      },
      payload: {
        aps: {
          sound: config.sound || 'default',
          badge: 1,
          'content-available': 1,
        },
      },
    },
    webpush: {
      headers: {
        Urgency: config.priority.android === 'high' ? 'high' : 'normal',
      },
    },
  };

  if (Array.isArray(tokens)) {
    return {
      ...baseMessage,
      tokens,
    } as admin.messaging.MulticastMessage;
  } else {
    return {
      ...baseMessage,
      token: tokens,
    } as admin.messaging.Message;
  }
}


/**
 * Send notification to a mobile user
 */
export async function sendNotificationToUser(
  userUID: string,
  message: string,
  title: string,
  data?: any
): Promise<void> {
  try {
    // Get user's FCM token
    const userDoc = await db.collection("mobile_users").doc(userUID).get();

    if (!userDoc.exists) {
      logger.warn(`User ${userUID} not found for notification`);
      return;
    }

    const userData = userDoc.data();
    const fcmToken = userData?.fcmToken;

    if (!fcmToken) {
      logger.debug(`No FCM token for user ${userUID}`);
      return;
    }

    // Determine notification priority based on type
    const notificationType = data?.type || "general";
    const isHighPriority = HIGH_PRIORITY_TYPES.includes(notificationType as any);
    
    // Use notification helpers for proper priority configuration
    const config: NotificationConfig = {
      title,
      body: message,
      data: {
        ...data,
        type: notificationType,
      },
      priority: isHighPriority 
        ? { android: 'high', ios: '10' }
        : { android: 'normal', ios: '5' },
      channelId: data?.channelId || (isHighPriority ? 'high_priority_channel' : 'default_channel'),
      sound: data?.sound || 'default',
    };
    
    const notificationMessage = createHighPriorityMessage(fcmToken, config) as admin.messaging.Message;

    await sendPushNotification(notificationMessage, `notification_to_user_${userUID}`);

  } catch (error) {
    logger.error(`Error sending notification to user ${userUID}:`, error);

    // If token is invalid, remove it
    if ((error as any).code === "messaging/invalid-registration-token" ||
      (error as any).code === "messaging/registration-token-not-registered") {
      await db.collection("mobile_users").doc(userUID).update({
        fcmToken: admin.firestore.FieldValue.delete(),
      });
    }
  }
}

/**
 * Send notification to multiple users
 */
export async function sendNotificationToUsers(
  userUIDs: string[],
  message: string,
  title: string,
  data?: any
): Promise<void> {
  const promises = userUIDs.map(uid => sendNotificationToUser(uid, message, title, data));
  await Promise.all(promises);
}

/**
 * Send notification about vehicle approval
 */
export async function sendVehicleApprovalNotification(
  driverUID: string,
  vehicleInfo: { brand: string, model: string, registrationNumber: string },
  approved: boolean,
  remark?: string
): Promise<void> {
  const title = approved ? "Vehicle Approved" : "Vehicle Rejected";
  const message = approved
    ? `Your vehicle ${vehicleInfo.brand} ${vehicleInfo.model} (${vehicleInfo.registrationNumber}) has been approved`
    : `Your vehicle ${vehicleInfo.brand} ${vehicleInfo.model} (${vehicleInfo.registrationNumber}) has been rejected${remark ? ": " + remark : ""}`;

  const notificationData = {
    type: "vehicle_approval",
    vehicleBrand: vehicleInfo.brand,
    vehicleModel: vehicleInfo.model,
    registrationNumber: vehicleInfo.registrationNumber,
    approved: approved.toString(),
    ...(remark && { remark }),
  };

  await sendNotificationToUser(driverUID, message, title, notificationData);
}

/**
 * Send notification about document expiry
 */
export async function sendDocumentExpiryNotification(
  userUID: string,
  documentName: string,
  documentType: string,
  daysUntilExpiry: number,
  expiryDate: Date
): Promise<void> {
  const title = daysUntilExpiry <= 0 ? "Document Expired" : "Document Expiring Soon";
  const message = daysUntilExpiry <= 0
    ? `Your ${documentName} has expired`
    : `Your ${documentName} expires in ${daysUntilExpiry} day${daysUntilExpiry !== 1 ? "s" : ""}`;

  const notificationData = {
    type: "document_expiry",
    documentName,
    documentType,
    daysUntilExpiry: daysUntilExpiry.toString(),
    expiryDate: expiryDate.toISOString(),
    urgency: daysUntilExpiry <= 1 ? "high" : daysUntilExpiry <= 7 ? "medium" : "low",
  };

  await sendNotificationToUser(userUID, message, title, notificationData);
}

/**
 * Send notification about vehicle assignment
 */
export async function sendVehicleAssignmentNotification(
  driverUID: string,
  vehicleInfo: { brand: string, model: string, registrationNumber: string },
  action: "assigned" | "unassigned"
): Promise<void> {
  const title = action === "assigned" ? "Vehicle Assigned" : "Vehicle Unassigned";
  const message = action === "assigned"
    ? `You have been assigned vehicle ${vehicleInfo.brand} ${vehicleInfo.model} (${vehicleInfo.registrationNumber})`
    : `Vehicle ${vehicleInfo.brand} ${vehicleInfo.model} (${vehicleInfo.registrationNumber}) has been unassigned from you`;

  const notificationData = {
    type: "vehicle_assignment",
    action,
    vehicleBrand: vehicleInfo.brand,
    vehicleModel: vehicleInfo.model,
    registrationNumber: vehicleInfo.registrationNumber,
  };

  await sendNotificationToUser(driverUID, message, title, notificationData);
}