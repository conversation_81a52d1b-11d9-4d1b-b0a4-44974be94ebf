import { admin } from "./config";
import { logger } from "firebase-functions/v2";
import { isProduction } from "./environment";

/**
 * Guard variable to control push notifications in development mode
 * Set to true to send actual push notifications in dev mode
 * Set to false to only log notifications in dev mode
 */
const ENABLE_PUSH_NOTIFICATIONS_IN_DEV = true;

/**
 * Send push notification with development mode guard
 */
export async function sendPushNotification(message: admin.messaging.Message, context?: string): Promise<void> {
  const isProd = isProduction();

  if (isProd || ENABLE_PUSH_NOTIFICATIONS_IN_DEV) {
    // Send the actual notification
    await admin.messaging().send(message);

    const environmentEmoji = isProd ? "📱" : "🔔";
    const environmentLabel = isProd ? "PRODUCTION" : "DEV MODE";

    logger.info(`${environmentEmoji} [${environmentLabel}] Push notification sent successfully`, {
      context,
      environment: isProd ? "production" : "development",
      to: "token" in message ? "single_device" : "topic",
      title: message.notification?.title,
    });
  } else {
    // Log the notification details in dev mode when disabled
    const token = "token" in message ? message.token : null;
    logger.info(`🔔 [DEV MODE - DISABLED] Push notification would have been sent:`, {
      context,
      environment: "development",
      title: message.notification?.title,
      body: message.notification?.body,
      data: message.data,
      token: token ? `${token.substring(0, 20)}...` : "N/A",
      android: message.android ? "configured" : "not_configured",
      apns: message.apns ? "configured" : "not_configured",
      webpush: message.webpush ? "configured" : "not_configured",
    });
  }
}
