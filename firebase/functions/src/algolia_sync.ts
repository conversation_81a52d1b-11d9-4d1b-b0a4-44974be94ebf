import { onDocumentWritten } from "firebase-functions/v2/firestore";
import { logger } from "firebase-functions/v2";
import { algoliasearch } from "algoliasearch";
import { db } from "./config";

// Get Algolia configuration from environment variables
// In local dev, these come from .env.json
// In production, these should be set as Firebase environment variables
let ALGOLIA_APP_ID = "";
let ALGOLIA_ADMIN_KEY = "";

// Load config based on environment
if (process.env.FUNCTIONS_EMULATOR) {
  // Local development: load from .env.json (use dev config)
  try {
    const envConfig = require("../.env.json");
    ALGOLIA_APP_ID = envConfig.algolia?.dev?.app_id || envConfig.algolia?.app_id || "";
    ALGOLIA_ADMIN_KEY = envConfig.algolia?.dev?.admin_key || envConfig.algolia?.admin_key || "";
  } catch (e) {
    logger.warn("No .env.json found. Create one from .env.json.example");
  }
} else {
  // Production: use Firebase config or prod config from .env.json
  const functions = require("firebase-functions");
  const config = functions.config();

  // Try Firebase config first, then environment variables, then .env.json prod config
  if (config.algolia?.app_id) {
    ALGOLIA_APP_ID = config.algolia.app_id;
    ALGOLIA_ADMIN_KEY = config.algolia.admin_key || "";
  } else if (process.env.ALGOLIA_APP_ID) {
    ALGOLIA_APP_ID = process.env.ALGOLIA_APP_ID;
    ALGOLIA_ADMIN_KEY = process.env.ALGOLIA_ADMIN_KEY || "";
  } else {
    // Fallback to .env.json prod config
    try {
      const envConfig = require("../.env.json");
      ALGOLIA_APP_ID = envConfig.algolia?.prod?.app_id || "";
      ALGOLIA_ADMIN_KEY = envConfig.algolia?.prod?.admin_key || "";
    } catch (e) {
      logger.warn("No .env.json found for production config");
    }
  }
}

// Initialize Algolia client
const algoliaClient = ALGOLIA_APP_ID && ALGOLIA_ADMIN_KEY ? algoliasearch(ALGOLIA_APP_ID, ALGOLIA_ADMIN_KEY) : null;

// Helper function to get index name with optional tenant suffix
function getIndexName(collection: string, tenantId?: string): string {
  return tenantId ? `${collection}_${tenantId}` : collection;
}

// Helper function to check if relevant data changed for Algolia sync  
function hasRelevantChanges(beforeData: any, afterData: any, collection?: string): boolean {
  if (!beforeData || !afterData) return true; // New document or deletion
  
  // Fields to ignore when checking for changes
  const ignoredFields = ['lastSeen', 'lastSeenDT', '_firestore', '_ref'];
  
  // Collection-specific ignored fields
  if (collection === 'mobile_users') {
    ignoredFields.push('fcmToken', 'fcmTokens'); // FCM tokens don't need Algolia sync
  }
  
  // Create comparison objects without ignored fields
  const beforeFiltered = { ...beforeData };
  const afterFiltered = { ...afterData };
  
  ignoredFields.forEach(field => {
    delete beforeFiltered[field];
    delete afterFiltered[field];
  });
  
  // Deep comparison (simple implementation)
  return JSON.stringify(beforeFiltered) !== JSON.stringify(afterFiltered);
}

// Helper function to sanitize data for Algolia
function sanitizeForAlgolia(data: any, collection?: string): any {
  const sanitized = { ...data };

  // Remove Firebase specific fields
  delete sanitized._firestore;
  delete sanitized._ref;
  
  // Remove fields that shouldn't be in Algolia
  delete sanitized.lastSeen;
  delete sanitized.lastSeenDT;
  delete sanitized.fcmToken;
  delete sanitized.fcmTokens;

  // Remove large/unnecessary fields for trips
  if (collection === 'trips') {
    // Remove route data (too large and not searchable)
    delete sanitized.routeData;
    delete sanitized.driverRouteData;
    delete sanitized.finalRouteData;
    delete sanitized.mapsPolylines;
    
    // Remove location objects (we'll keep string addresses)
    delete sanitized.startLocation;
    delete sanitized.arrivalLocation;
    delete sanitized.driverLocation;
    
    // Remove other non-searchable large data
    delete sanitized.bounds;
    delete sanitized.estimations;
    delete sanitized.fareBreakdown;
    delete sanitized.routeLegs;
    delete sanitized.waypoints;
    
    // Remove sensitive or unnecessary fields
    delete sanitized.stripePaymentIntentId;
    delete sanitized.stripeChargeId;
    delete sanitized.fcmTokens;
    delete sanitized.chatSessionId;
    delete sanitized.previousDrivers;
    
    // Remove additional large/unnecessary fields
    delete sanitized.routeOverviews;
    delete sanitized.tripConfiguration;
    delete sanitized.routeDataIds;
  }

  // Convert timestamps to ISO strings
  Object.keys(sanitized).forEach((key) => {
    if (sanitized[key] && sanitized[key].toDate) {
      sanitized[key] = sanitized[key].toDate().toISOString();
    }
  });

  return sanitized;
}

// Sync trips to Algolia
export const syncTripsToAlgolia = onDocumentWritten(
  {
    document: "trips/{tripId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const tripId = event.params.tripId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Handle deletion
      if (!afterData && beforeData) {
        const tenantId = beforeData.tenantId;
        const indexName = getIndexName("trips", tenantId);
        await algoliaClient.deleteObject({
          indexName,
          objectID: tripId,
        });
        logger.info(`Deleted trip ${tripId} from Algolia index`);
        return;
      }

      // Handle create/update
      if (afterData) {
        // Check if relevant data changed
        if (!hasRelevantChanges(beforeData, afterData, 'trips')) {
          logger.info(`Skipping trips Algolia sync for ${tripId} - no relevant changes`);
          return;
        }
        
        const tenantId = afterData.tenantId;
        const indexName = getIndexName("trips", tenantId);

        // Fetch related data to enrich the record
        const [passenger, driver] = await Promise.all([
          afterData.uidPassenger ? db.collection("mobile_users").doc(afterData.uidPassenger).get() : null,
          afterData.uidChosenDriver ? db.collection("mobile_users").doc(afterData.uidChosenDriver).get() : null,
        ]);
        
        // Get vehicle through driver's tenant state
        let vehicle = null;
        if (afterData.uidChosenDriver) {
          const driverTenantState = await db.collection("mobile_users")
            .doc(afterData.uidChosenDriver)
            .collection("tenant_states")
            .doc(tenantId)
            .get();
            
          if (driverTenantState.exists && driverTenantState.data()?.currentVehicleLinkingId) {
            const vehicleLinking = await db.collection("tenants")
              .doc(tenantId)
              .collection("vehicles_linking")
              .doc(driverTenantState.data()!.currentVehicleLinkingId)
              .get();
              
            if (vehicleLinking.exists && vehicleLinking.data()?.vehicleId) {
              vehicle = await db.collection("vehicles").doc(vehicleLinking.data()!.vehicleId).get();
            }
          }
        }
        
        // Fallback to vehicleId if it exists (for backward compatibility)
        if (!vehicle && afterData.vehicleId) {
          vehicle = await db.collection("vehicles").doc(afterData.vehicleId).get();
        }

        // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
        // Any changes here must be reflected in the on-demand sync script
        const record = {
          objectID: tripId,
          ...sanitizeForAlgolia(afterData, 'trips'),
          // Add related fields for search
          passengerName: passenger?.data()?.displayName || "",
          passengerPhone: passenger?.data()?.phoneNumber || "",
          driverName: driver?.data()?.displayName || "",
          driverPhone: driver?.data()?.phoneNumber || "",
          vehiclePlate: vehicle?.data()?.registrationNumber || "",
          vehicleModel: vehicle?.data()?.model || "",
          // Ensure tenantId is always included
          tenantId: tenantId,
        };

        await algoliaClient.saveObject({
          indexName,
          body: record,
        });
        logger.info(`Synced trip ${tripId} to Algolia index`);
      }
    } catch (error) {
      logger.error(`Error syncing trip ${tripId} to Algolia:`, error);
    }
  }
);

// Sync mobile_users to Algolia
export const syncMobileUsersToAlgolia = onDocumentWritten(
  {
    document: "mobile_users/{userId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const userId = event.params.userId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Handle deletion - need to delete from all tenant indexes
      if (!afterData && beforeData) {
        // Get tenant IDs from the deleted user data
        const tenantIds = beforeData.tenantIDs || [];
        
        // Delete from each tenant's index
        for (const tenantId of tenantIds) {
          const indexName = getIndexName("mobile_users", tenantId);
          await algoliaClient.deleteObject({
            indexName,
            objectID: userId,
          });
          logger.info(`Deleted user ${userId} from Algolia index ${indexName}`);
        }
        return;
      }

      // Handle create/update
      if (afterData) {
        // Check if relevant data changed (skip if only lastSeen or FCM tokens changed)
        if (!hasRelevantChanges(beforeData, afterData, 'mobile_users')) {
          logger.info(`Skipping mobile_users Algolia sync for ${userId} - no relevant changes`);
          return;
        }
        
        // Get tenant states from subcollection
        const tenantStatesSnapshot = await db.collection("mobile_users")
          .doc(userId)
          .collection("tenant_states")
          .get();
        
        // Sync to each tenant the user belongs to
        for (const tenantStateDoc of tenantStatesSnapshot.docs) {
          const tenantId = tenantStateDoc.id;
          const tenantState = tenantStateDoc.data();
          
          // Skip inactive tenant states
          if (!tenantState.isActive) {
            continue;
          }
          
          const indexName = getIndexName("mobile_users", tenantId);
          
          // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
          // Any changes here must be reflected in the on-demand sync script
          const record = {
            objectID: userId,
            ...sanitizeForAlgolia(afterData),
            // Include tenant-specific state
            tenantId: tenantId,
            isDriverConfirmed: tenantState.isDriverConfirmed || false,
            isServiceActive: tenantState.isServiceActive || false,
            driverTags: tenantState.driverTags || [],
            currentVehicleLinkingId: tenantState.currentVehicleLinkingId || null,
          };

          await algoliaClient.saveObject({
            indexName,
            body: record,
          });
          logger.info(`Synced user ${userId} to Algolia index ${indexName}`);
        }
      }
    } catch (error) {
      logger.error(`Error syncing user ${userId} to Algolia:`, error);
    }
  }
);

// Sync vehicles to Algolia
export const syncVehiclesToAlgolia = onDocumentWritten(
  {
    document: "vehicles/{vehicleId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const vehicleId = event.params.vehicleId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Handle deletion
      if (!afterData && beforeData) {
        const tenantId = beforeData.tenantId;
        const indexName = getIndexName("vehicles", tenantId);
        await algoliaClient.deleteObject({
          indexName,
          objectID: vehicleId,
        });
        logger.info(`Deleted vehicle ${vehicleId} from Algolia index`);
        return;
      }

      // Handle create/update
      if (afterData) {
        // Check if relevant data changed
        if (!hasRelevantChanges(beforeData, afterData, 'vehicles')) {
          logger.info(`Skipping vehicles Algolia sync for ${vehicleId} - no relevant changes`);
          return;
        }
        
        const tenantId = afterData.tenantId;
        const indexName = getIndexName("vehicles", tenantId);

        // Fetch driver data if assigned
        const driver = afterData.currentDriverId
          ? await db.collection("mobile_users").doc(afterData.currentDriverId).get()
          : null;

        // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
        // Any changes here must be reflected in the on-demand sync script
        // NOTE: This needs to be updated to include vehicle linking data
        const record = {
          objectID: vehicleId,
          ...sanitizeForAlgolia(afterData),
          // Add driver info for search
          currentDriverName: driver?.data()?.displayName || "",
          currentDriverPhone: driver?.data()?.phoneNumber || "",
          // TODO: Add vehicle linking data (currentDriverId, tenantApproved, isOwnedByTenant, tenantRemark)
          // Ensure tenantId is always included
          tenantId: tenantId,
        };

        await algoliaClient.saveObject({
          indexName,
          body: record,
        });
        logger.info(`Synced vehicle ${vehicleId} to Algolia index`);
      }
    } catch (error) {
      logger.error(`Error syncing vehicle ${vehicleId} to Algolia:`, error);
    }
  }
);

// Sync payments to Algolia
export const syncPaymentsToAlgolia = onDocumentWritten(
  {
    document: "payments/{paymentId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const paymentId = event.params.paymentId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Handle deletion
      if (!afterData && beforeData) {
        const tenantId = beforeData.tenantId;
        const indexName = getIndexName("payments", tenantId);
        await algoliaClient.deleteObject({
          indexName,
          objectID: paymentId,
        });
        logger.info(`Deleted payment ${paymentId} from Algolia index`);
        return;
      }

      // Handle create/update
      if (afterData) {
        // Check if relevant data changed
        if (!hasRelevantChanges(beforeData, afterData, 'payments')) {
          logger.info(`Skipping payments Algolia sync for ${paymentId} - no relevant changes`);
          return;
        }
        
        const tenantId = afterData.tenantId;
        const indexName = getIndexName("payments", tenantId);

        // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
        // Any changes here must be reflected in the on-demand sync script
        const record = {
          objectID: paymentId,
          ...sanitizeForAlgolia(afterData),
          // Ensure tenantId is always included
          tenantId: tenantId,
        };

        await algoliaClient.saveObject({
          indexName,
          body: record,
        });
        logger.info(`Synced payment ${paymentId} to Algolia index`);
      }
    } catch (error) {
      logger.error(`Error syncing payment ${paymentId} to Algolia:`, error);
    }
  }
);

// Sync chat_sessions to Algolia
export const syncChatSessionsToAlgolia = onDocumentWritten(
  {
    document: "chat_sessions/{sessionId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const sessionId = event.params.sessionId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Handle deletion
      if (!afterData && beforeData) {
        const tenantId = beforeData.tenantId;
        const indexName = getIndexName("chat_sessions", tenantId);
        await algoliaClient.deleteObject({
          indexName,
          objectID: sessionId,
        });
        logger.info(`Deleted chat session ${sessionId} from Algolia index`);
        return;
      }

      // Handle create/update
      if (afterData) {
        // Check if relevant data changed
        if (!hasRelevantChanges(beforeData, afterData, 'chat_sessions')) {
          logger.info(`Skipping chat_sessions Algolia sync for ${sessionId} - no relevant changes`);
          return;
        }
        
        const tenantId = afterData.tenantId;
        const indexName = getIndexName("chat_sessions", tenantId);

        // Fetch participants data
        const participantPromises = (afterData.participants || []).map((participantId: string) =>
          db.collection("mobile_users").doc(participantId).get()
        );
        const participants = await Promise.all(participantPromises);

        // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
        // Any changes here must be reflected in the on-demand sync script
        const record = {
          objectID: sessionId,
          ...sanitizeForAlgolia(afterData),
          // Add participant names for search
          participantNames: participants.map((p) => p.data()?.name || "").filter((name) => name),
          // Ensure tenantId is always included
          tenantId: tenantId,
        };

        await algoliaClient.saveObject({
          indexName,
          body: record,
        });
        logger.info(`Synced chat session ${sessionId} to Algolia index`);
      }
    } catch (error) {
      logger.error(`Error syncing chat session ${sessionId} to Algolia:`, error);
    }
  }
);

// Sync mobile user tenant states changes to Algolia
export const syncMobileUserTenantStatesToAlgolia = onDocumentWritten(
  {
    document: "mobile_users/{userId}/tenant_states/{tenantId}",
    region: "europe-west3",
  },
  async (event) => {
    if (!algoliaClient) {
      logger.warn("Algolia client not initialized. Skipping sync.");
      return;
    }

    const userId = event.params.userId;
    const tenantId = event.params.tenantId;
    const beforeData = event.data?.before.exists ? event.data.before.data() : null;
    const afterData = event.data?.after.exists ? event.data.after.data() : null;

    try {
      // Get the user document
      const userDoc = await db.collection("mobile_users").doc(userId).get();
      
      if (!userDoc.exists) {
        logger.warn(`User ${userId} not found, skipping tenant state sync`);
        return;
      }
      
      const userData = userDoc.data()!;
      const indexName = getIndexName("mobile_users", tenantId);

      // Handle deletion or deactivation
      if (!afterData || !afterData.isActive) {
        await algoliaClient.deleteObject({
          indexName,
          objectID: userId,
        });
        logger.info(`Removed user ${userId} from Algolia index ${indexName} (tenant state deleted/deactivated)`);
        return;
      }

      // Handle create/update
      if (afterData && afterData.isActive) {
        // Check if relevant tenant state data changed
        if (!hasRelevantChanges(beforeData, afterData, 'tenant_states')) {
          logger.info(`Skipping tenant state Algolia sync for ${userId} in tenant ${tenantId} - no relevant changes`);
          return;
        }
        
        // IMPORTANT: Keep this data structure in sync with firebase/data-tools/on-demand/algolia-sync.js
        // Any changes here must be reflected in the on-demand sync script
        const record = {
          objectID: userId,
          ...sanitizeForAlgolia(userData),
          // Include tenant-specific state
          tenantId: tenantId,
          isDriverConfirmed: afterData.isDriverConfirmed || false,
          isServiceActive: afterData.isServiceActive || false,
          driverTags: afterData.driverTags || [],
          currentVehicleLinkingId: afterData.currentVehicleLinkingId || null,
        };

        await algoliaClient.saveObject({
          indexName,
          body: record,
        });
        logger.info(`Synced user ${userId} tenant state to Algolia index ${indexName}`);
      }
    } catch (error) {
      logger.error(`Error syncing user ${userId} tenant state to Algolia:`, error);
    }
  }
);
