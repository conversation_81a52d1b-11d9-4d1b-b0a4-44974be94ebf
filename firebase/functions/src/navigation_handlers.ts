import { db } from "./config";
import { logger } from "firebase-functions/v2";
import { createRouteData, Trip } from "./navigation";
import { sendDriverNotification, sendPassengerNotification } from "./notifications";
import { normalizeDataForFirestore } from "./utils";
import { RouteDataDocument, RouteType } from "./models/RouteDataDocument";
import { Timestamp } from "firebase-admin/firestore";
import { getTenantCollection } from "./tenant_utils";

/**
 * Handles the 'preparing' status of a trip.
 * - Calculates and stores route data if not present
 * - If transitioning from another status with a driver:
 *   - Releases the previous driver (clears occupiedByTripId)
 *   - Adds them to skippedDriverIds list
 *
 * @param trip - The trip document data
 * @param event - The Firestore trigger event
 */
export async function handlePreparingStatus(trip: Trip, event: any, tenantId: string) {
  const update: any = {};

  // Check if this is a new trip without route data
  const hasRouteDataIds = trip.routeDataIds?.main;
  const hasEmbeddedRouteData = trip.routeData;

  if (!hasRouteDataIds && !hasEmbeddedRouteData) {
    // This is a new trip - create route data in separate collection
    const routeData = await createRouteData(trip.startLocation, trip.arrivalLocation, "HIGH_QUALITY");

    // Create route data document
    const routeDataDoc: RouteDataDocument = {
      tripId: event.params.tripId,
      routeType: "main" as RouteType,
      routeData: routeData,
      createdAt: Timestamp.now(),
    };

    const routeDataRef = await getTenantCollection(tenantId, "route_data").add(routeDataDoc);

    // Update trip with route data ID only
    if (!update.routeDataIds) {
      update.routeDataIds = {};
    }
    update.routeDataIds.main = routeDataRef.id;

    logger.info("PREPARING: Route data created in separate collection ✅", {
      tripId: event.params.tripId,
      routeDataId: routeDataRef.id,
      routeData,
    });
  }

  // If the previous status was not 'preparing', release the previous driver from the trip and add him to the skipped drivers list
  const previousTripData = event.data?.before.data();
  if (previousTripData && previousTripData.status && previousTripData?.status !== "preparing") {
    const previousDriverId = previousTripData.uidChosenDriver;
    if (previousDriverId) {
      const previousDriverRef = db.collection("mobile_users").doc(previousDriverId);
      const driverUpdateData = normalizeDataForFirestore(
        { occupiedByTripId: null },
        `handlePreparingStatus:previousDriverRef:update:${previousDriverId}`
      );
      if (Object.keys(driverUpdateData).length > 0) {
        await previousDriverRef.update(driverUpdateData);
      }

      // Check if this is a timeout scenario by looking at the current trip data
      const currentTripData = event.data?.after.data();
      const isTimeout = currentTripData?.timeoutReason || currentTripData?.lastTimeoutAt;

      // Only add to skippedDriverIds if it's NOT a timeout
      // For timeouts, we want the driver to be available for selection again
      if (!isTimeout) {
        const skippedDriverIds = previousTripData.skippedDriverIds || [];
        if (!skippedDriverIds.includes(previousDriverId)) {
          skippedDriverIds.push(previousDriverId);
        }
        update.skippedDriverIds = skippedDriverIds;
        logger.info("PREPARING: Previous driver added to skipped list (not a timeout)", {
          tripId: event.params.tripId,
          previousDriverId,
        });
      } else {
        logger.info("PREPARING: Previous driver released but NOT added to skipped list (timeout scenario)", {
          tripId: event.params.tripId,
          previousDriverId,
          timeoutReason: currentTripData?.timeoutReason,
        });
      }
    }
  }

  if (Object.keys(update).length > 0) {
    const normalizedUpdate = normalizeDataForFirestore(
      update,
      `handlePreparingStatus:event.data.after.ref:update:${event.params.tripId}`
    );
    if (Object.keys(normalizedUpdate).length > 0) {
      await event.data?.after.ref.update(normalizedUpdate);
    }
  }
}

/**
 * Handles the 'requestingDriver' status of a trip.
 * - Calculates route from driver's location to pickup point
 * - Validates if driver's service is active
 * - Sends push notification to the chosen driver
 * - If driver's service is inactive:
 *   - Clears driver assignment
 *   - Returns trip to 'preparing' status
 *
 * @param trip - The trip document data
 * @param event - The Firestore trigger event
 */
export async function handleRequestingDriverStatus(trip: Trip, event: any, tenantId: string) {
  if (!trip.driverLocation || !trip.uidChosenDriver) {
    logger.warn("REQUESTING_DRIVER: Missing driver information", { tripId: event.params.tripId });
    return;
  }

  const update: any = {};

  // Check if driver route data exists
  const hasDriverRouteDataId = trip.routeDataIds?.driver;
  const hasEmbeddedDriverRouteData = trip.driverRouteData;

  if (!hasDriverRouteDataId && !hasEmbeddedDriverRouteData) {
    // Create driver route data in separate collection for new trips
    const driverRouteData = await createRouteData(trip.driverLocation, trip.startLocation);

    // Create route data document
    const routeDataDoc: RouteDataDocument = {
      tripId: event.params.tripId,
      routeType: "driver" as RouteType,
      routeData: driverRouteData,
      createdAt: Timestamp.now(),
    };

    const routeDataRef = await getTenantCollection(tenantId, "route_data").add(routeDataDoc);

    // Update trip with route data ID only
    if (!update.routeDataIds) {
      update.routeDataIds = trip.routeDataIds || {};
    }
    update.routeDataIds.driver = routeDataRef.id;

    logger.info("REQUESTING_DRIVER: Driver route data created in separate collection ✅", {
      tripId: event.params.tripId,
      routeDataId: routeDataRef.id,
    });
  }

  if (!trip.driverNotificationSent) {
    const driverData = await getUserData(trip.uidChosenDriver);
    const driverTenantState = await db
      .collection("mobile_users")
      .doc(trip.uidChosenDriver)
      .collection("tenant_states")
      .doc(tenantId)
      .get();

    const isDriverServiceActive = driverTenantState.exists && driverTenantState.data()?.isServiceActive === true;

    if (!isDriverServiceActive) {
      logger.warn("REQUESTING_DRIVER: Driver's service is disabled", { tripId: event.params.tripId });
      Object.assign(update, {
        uidChosenDriver: null,
        status: "preparing",
        driverRouteData: null,
        driverLocation: null,
      });
      // Also clear driver route data ID if it exists
      if (update.routeDataIds) {
        update.routeDataIds.driver = null;
      }
    } else if (driverData?.fcmToken) {
      // Get trip data for notification
      const pickupTime = trip.pickupTime?.toDate();
      const startLocation = trip.startLocationName;
      const destinationLocation = trip.arrivalLocationName;

      await sendDriverNotification(driverData.fcmToken, {
        tripId: event.params.tripId,
        pickupTime,
        startLocation,
        destinationLocation,
        driverUid: trip.uidChosenDriver,
      });
      logger.info("REQUESTING_DRIVER: Push notification sent ✅", {
        tripId: event.params.tripId,
        hasPickupTime: !!pickupTime,
      });
      update.driverNotificationSent = true;
    } else {
      logger.warn("REQUESTING_DRIVER: No FCM token for the chosen driver", { tripId: event.params.tripId });
    }
  }

  if (Object.keys(update).length > 0) {
    const normalizedUpdate = normalizeDataForFirestore(
      update,
      `handleRequestingDriverStatus:event.data.after.ref:update:${event.params.tripId}`
    );
    if (Object.keys(normalizedUpdate).length > 0) {
      await event.data?.after.ref.update(normalizedUpdate);
    }
  }
}

/**
 * Handles the 'driverApproaching' status when a driver accepts a trip.
 * Uses a transaction to:
 * - Mark the driver as occupied with this specific trip
 * - Cancel all other pending trip requests for this driver
 * - Sends notification to passenger that driver is approaching
 * - Ensures data consistency across multiple operations
 *
 * @param trip - The trip document data
 * @param event - The Firestore trigger event
 */
export async function handleDriverApproachingStatus(trip: Trip, event: any, tenantId: string) {
  if (!trip.uidChosenDriver) {
    logger.warn("DRIVER_APPROACHING: Missing driver information", { tripId: event.params.tripId });
    return;
  }

  const driverRef = db.collection("mobile_users").doc(trip.uidChosenDriver);

  // Send driver_moving notification to passenger when driver starts approaching
  try {
    if (trip.uidPassenger) {
      const passengerData = await getUserData(trip.uidPassenger);
      if (passengerData?.fcmToken) {
        await sendPassengerNotification(
          passengerData.fcmToken,
          "driver_moving",
          trip.uidPassenger,
          { tripId: event.params.tripId },
          undefined, // no custom body
          undefined, // no custom time
          tenantId
        );
        logger.info("DRIVER_APPROACHING: Driver moving notification sent ✅", {
          tripId: event.params.tripId,
          passengerUserId: trip.uidPassenger,
        });
      }
    }
  } catch (error) {
    logger.error("DRIVER_APPROACHING: Error sending driver moving notification", {
      tripId: event.params.tripId,
      error,
    });
  }

  await db.runTransaction(async (transaction) => {
    const driverDoc = await transaction.get(driverRef);
    const driverData = driverDoc.data();
    if (!driverData) {
      logger.warn("DRIVER_APPROACHING: Driver data not found", { tripId: event.params.tripId });
      return;
    }

    // Read all trips before performing any writes
    const tripsQuery = getTenantCollection(tenantId, "trips")
      .where("uidChosenDriver", "==", trip.uidChosenDriver)
      .where("status", "==", "requestingDriver");
    const tripsSnapshot = await transaction.get(tripsQuery);

    // Mark the driver as occupied by this trip
    const driverOccupiedUpdate = normalizeDataForFirestore(
      { occupiedByTripId: event.params.tripId },
      `handleDriverApproachingStatus:driverRef:update:${trip.uidChosenDriver}`
    );
    if (Object.keys(driverOccupiedUpdate).length > 0) {
      transaction.update(driverRef, driverOccupiedUpdate);
    }

    // Cancel other trip requests for the same driver
    let count = 0;
    tripsSnapshot.forEach((doc) => {
      if (doc.id !== event.params.tripId) {
        const otherTripUpdate = normalizeDataForFirestore(
          {
            status: "preparing",
            uidChosenDriver: null,
            driverRouteData: null,
            driverLocation: null,
          },
          `handleDriverApproachingStatus:transaction:updateOtherTrip:${doc.id}`
        );
        if (Object.keys(otherTripUpdate).length > 0) {
          transaction.update(doc.ref, otherTripUpdate);
        }
        count++;
      }
    });
    logger.info("DRIVER_APPROACHING: Cancelled other trip requests", { tripId: event.params.tripId, count });
  });

  logger.info("DRIVER_APPROACHING: Driver occupied by trip", { tripId: event.params.tripId, driverUserId: trip.uidChosenDriver });

  // Send notification to passenger that driver is approaching
  try {
    if (trip.uidPassenger) {
      const passengerData = await getUserData(trip.uidPassenger);
      if (passengerData?.fcmToken) {
        await sendPassengerNotification(
          passengerData.fcmToken,
          "driver_moving",
          trip.uidPassenger,
          { tripId: event.params.tripId },
          undefined, // no custom body
          undefined, // no custom time
          tenantId
        );
        logger.info("DRIVER_APPROACHING: Passenger notification sent ✅", {
          tripId: event.params.tripId,
          passengerUserId: trip.uidPassenger,
        });
      } else {
        logger.warn("DRIVER_APPROACHING: No FCM token for passenger", {
          tripId: event.params.tripId,
          passengerUserId: trip.uidPassenger,
        });
      }
    }
  } catch (error) {
    logger.error("DRIVER_APPROACHING: Error sending passenger notification", {
      tripId: event.params.tripId,
      error,
    });
  }
}

/**
 * Handles the 'driverAwaiting' status when a driver arrives at pickup location.
 * - Sends notification to passenger that driver has arrived
 *
 * @param trip - The trip document data
 * @param event - The Firestore trigger event
 */
export async function handleDriverAwaitingStatus(trip: Trip, event: any, tenantId: string) {
  if (!trip.uidChosenDriver || !trip.uidPassenger) {
    logger.warn("DRIVER_AWAITING: Missing trip information", { tripId: event.params.tripId });
    return;
  }

  // Send notification to passenger that driver has arrived
  try {
    const passengerData = await getUserData(trip.uidPassenger);
    if (passengerData?.fcmToken) {
      await sendPassengerNotification(
        passengerData.fcmToken,
        "driver_arrived",
        trip.uidPassenger,
        { tripId: event.params.tripId },
        undefined, // no custom body
        undefined, // no custom time
        tenantId
      );
      logger.info("DRIVER_AWAITING: Passenger notification sent ✅", {
        tripId: event.params.tripId,
        passengerUserId: trip.uidPassenger,
      });
    } else {
      logger.warn("DRIVER_AWAITING: No FCM token for passenger", {
        tripId: event.params.tripId,
        passengerUserId: trip.uidPassenger,
      });
    }
  } catch (error) {
    logger.error("DRIVER_AWAITING: Error sending passenger notification", {
      tripId: event.params.tripId,
      error,
    });
  }
}

/**
 * Handles the 'paid' status of a trip.
 * - Always releases the driver from trip occupation
 * - Cleans up the driver's occupiedByTripId field
 *
 * @param trip - The trip document data
 * @param event - The Firestore trigger event
 */
export async function handlePaidStatus(trip: Trip, event: any, tenantId: string) {
  // Always release the driver when trip is paid
  if (trip.uidChosenDriver) {
    const driverRef = db.collection("mobile_users").doc(trip.uidChosenDriver);
    const driverDoc = await driverRef.get();
    const driverData = driverDoc.data();

    if (driverData?.occupiedByTripId === event.params.tripId) {
      const paidStatusDriverUpdate = normalizeDataForFirestore(
        { occupiedByTripId: null },
        `handlePaidStatus:driverRef:update:${trip.uidChosenDriver}`
      );
      if (Object.keys(paidStatusDriverUpdate).length > 0) {
        await driverRef.update(paidStatusDriverUpdate);
      }
      logger.info("PAID: Driver released from paid trip ✅", {
        tripId: event.params.tripId,
        driverUserId: trip.uidChosenDriver,
        driverDismissed: trip.driverDismissed || false,
      });
    }
  }
}

/**
 * Retrieves user data from the mobile_users collection.
 * Note: mobile_users collection is global (shared across all tenants) as per multi-tenant architecture.
 *
 * @param uid - The user's unique identifier
 * @returns The user's document data or undefined if not found
 */
export async function getUserData(uid: string) {
  const doc = await db.collection("mobile_users").doc(uid).get();
  return doc.data();
}

/**
 * Handles cleanup when a trip is deleted.
 * - If a driver was occupied by this trip:
 *   - Removes the trip occupation from driver's record
 *
 * @param trip - The trip document data before deletion
 * @param tripId - The ID of the deleted trip
 * @param tenantId - The tenant ID (for consistency, though mobile_users is global)
 */
export async function handleTripDeletion(trip: Trip, tripId: string, _tenantId: string) {
  // _tenantId parameter is for consistency with other handlers, but mobile_users is global
  if (trip.uidChosenDriver) {
    // Note: mobile_users collection is global (shared across all tenants)
    const driverRef = db.collection("mobile_users").doc(trip.uidChosenDriver);
    const driverDoc = await driverRef.get();
    const driverData = driverDoc.data();

    if (driverData?.occupiedByTripId === tripId) {
      const deletionDriverUpdate = normalizeDataForFirestore(
        { occupiedByTripId: null },
        `handleTripDeletion:driverRef:update:${trip.uidChosenDriver}`
      );
      if (Object.keys(deletionDriverUpdate).length > 0) {
        await driverRef.update(deletionDriverUpdate);
      }
      logger.info("DELETED: Driver released from deleted trip ✅", { tripId, driverUserId: trip.uidChosenDriver });
    }
  }
}
