rules_version = '2';

// ⚠️  WARNING: BEFORE MODIFYING THESE SECURITY RULES ⚠️
// 
// 📖 REQUIRED READING: Please read MULTI-TENANT.md in the project root first!
//
// This project uses a multi-tenant architecture where:
// - Multiple taxi companies operate independently within the same Firebase project
// - Each tenant has isolated data while sharing common infrastructure
// - Admins have different roles and permissions per tenant
// - Mobile users can work across multiple tenants with different states
//
// Key Architecture Points:
// - Global collections: admin_users, mobile_users, tenants, vehicles, etc.
// - Tenant-specific collections: /tenants/{tenantId}/trips, /tenants/{tenantId}/payments, etc.
// - Role hierarchy: SUPER_ADMIN (2) only in 'fiaranow', ADMIN (1), MANAGER (0)
// - Vehicle management: Two-layer system (vehicles_linking + vehicle_assignments)
// - Service status: Source of truth in MobileUserTenantState.isServiceActive
//
// 🚨 CRITICAL: Changes to these rules affect ALL tenants and mobile apps
// 🚨 ALWAYS test with Firebase emulator before deploying
// 🚨 UPDATE MULTI-TENANT.md if you modify the tenant access patterns

service cloud.firestore {
  match /databases/{database}/documents {
    // --------------------------
    // Admin users collection
    // --------------------------

    // Check if user is an active admin
    function isActiveAdmin(uid) {
      let admin = getAdminUser(uid);
      return admin != null && admin.isActive == true;
    }
  
    // Helper function to get admin user document
    function getAdminUser(uid) {
      return get(/databases/$(database)/documents/admin_users/$(uid)).data;
    }

    // Tenant access control functions
    function getTenantAdminAccess(uid, tenantId) {
      return get(/databases/$(database)/documents/admin_users/$(uid)/tenants/$(tenantId)).data;
    }

    function hasActiveTenantAccess(uid, tenantId) {
      let tenantAccess = getTenantAdminAccess(uid, tenantId);
      return tenantAccess != null && tenantAccess.isActive == true;
    }

    function isSuperAdmin(uid, tenantId) {
      let tenantAccess = getTenantAdminAccess(uid, tenantId);
      return tenantAccess != null && tenantAccess.isActive == true && tenantAccess.role == 2;
    }

    function hasMinimumRole(uid, tenantId, minRole) {
      let tenantAccess = getTenantAdminAccess(uid, tenantId);
      return tenantAccess != null && tenantAccess.isActive == true && tenantAccess.role >= minRole;
    }

    // --------------------------
    // Collection Group Rules for Documents
    // --------------------------
    // Rules for collection group queries on driver_documents and vehicle_documents
    match /{path=**}/driver_documents/{docId} {
      // Allow list/query operations for active admins (needed for collectionGroup queries)
      allow list: if isActiveAdmin(request.auth.uid);
      
      // Allow read for document owner or active admins
      allow read: if isActiveAdmin(request.auth.uid);
      
      // Allow write for active admins (for status updates)
      allow write: if isActiveAdmin(request.auth.uid);
    }
    
    match /{path=**}/vehicle_documents/{docId} {
      // Allow list/query operations for active admins (needed for collectionGroup queries)
      allow list: if isActiveAdmin(request.auth.uid);
      
      // Allow read for active admins
      allow read: if isActiveAdmin(request.auth.uid);
      
      // Allow write for active admins (for status updates)
      allow write: if isActiveAdmin(request.auth.uid);
    }
    
    // Rules for collection group queries on tenant_states (needed for driver ratings)
    match /{path=**}/tenant_states/{tenantId} {
      // Allow list/query operations for active admins (needed for collectionGroup queries)
      allow list: if isActiveAdmin(request.auth.uid);
      
      // Allow read for active admins
      allow read: if isActiveAdmin(request.auth.uid);
    }

    match /admin_users/{userId} {
      // Helper function to check if this is a valid new admin creation
      function isValidNewAdmin() {
        return !exists(/databases/$(database)/documents/admin_users/$(request.auth.uid))
        && request.resource.data.isActive == false;
      }

      // Allow get of own document even if inactive (for UI feedback)
      allow get: if request.auth.uid == userId;

      // Allow list/query operations for active admins
      allow list: if isActiveAdmin(request.auth.uid);

      // Allow initial creation with restricted fields
      allow create: if request.auth.uid == userId && isValidNewAdmin();

      // Allow updates to own document, except for restricted fields
      // This allows FCM token updates and other non-restricted field updates
      allow update: if request.auth.uid == userId
        && !request.resource.data.diff(resource.data).affectedKeys()
            .hasAny(['isActive']);

      // Only active admins can write to OTHER admin documents (not their own)
      allow write: if isActiveAdmin(request.auth.uid) && request.auth.uid != userId;
    }

    // --------------------------
    // Mobile users collection
    // --------------------------
    match /mobile_users/{userId} {
      // Allow users to read their own document
      allow get: if request.auth.uid == userId;
      
      // Allow authenticated users to read any mobile_users document 
      // This is needed for passengers to track their driver's position
      // The app logic ensures only appropriate tracking occurs
      allow get: if request.auth != null;

      // Allow list/query operations for:
      // - active admins OR
      // - authenticated users querying for drivers
      allow list: if isActiveAdmin(request.auth.uid) || request.auth != null;

      // Allow users to create/update their own document OR if user is admin
      allow create, update: if (request.auth.uid == userId && request.resource.data.uid == userId) || isActiveAdmin(request.auth.uid);

      // Delete is not allowed for anyone
      allow delete: if false;

      // --------------------------
      // User preferences subcollection
      // --------------------------
      match /preferences/{prefId} {
        // Allow users to read/write their own preferences
        allow read: if request.auth.uid == userId || isActiveAdmin(request.auth.uid);
        allow write: if request.auth.uid == userId || isActiveAdmin(request.auth.uid);
      }
      
      // --------------------------
      // Driver documents subcollection
      // --------------------------
      match /driver_documents/{docId} {
        // Allow drivers to read their own documents
        allow read: if request.auth.uid == userId || isActiveAdmin(request.auth.uid);
        
        // Allow drivers to create/update their own documents OR admins to update any document
        allow create: if request.auth.uid == userId;
        allow update: if request.auth.uid == userId || isActiveAdmin(request.auth.uid);
        
        // No deletes allowed
        allow delete: if false;
      }
    }

    // --------------------------
    // Configurations collection
    // --------------------------
    match /configurations/{configId} {
      // Allow public read for trip configuration changes when the document ID is "tripConfiguration",
      // otherwise only allow reads if the user is authenticated.
      allow read: if configId == 'tripConfiguration' || request.auth != null;
      
      // Only active admins can create/update configurations
      allow create, update: if isActiveAdmin(request.auth.uid);
      
      // Delete is not allowed for anyone
      allow delete: if false;
    }

    // --------------------------
    // Global configurations collection
    // --------------------------
    match /global_configurations/{configId} {
      // Anyone can read global configurations
      allow read: if true;

      // No one can write or delete global configurations
      // These should only be updated through backend functions
      allow write: if false;
    }

    // --------------------------
    // Payments collection
    // --------------------------
    match /payments/{paymentId} {
      // Helper function to check if user is involved in the payment
      function isPaymentParticipant() {
        let payment = resource.data;
        return request.auth.uid == payment.customerId || 
               request.auth.uid == payment.driverId;
      }

      // Allow read if user is the customer or driver involved in the payment
      // OR if user is an active admin
      allow read: if isPaymentParticipant() || isActiveAdmin(request.auth.uid);

      // Only active admins can create payments
      allow create: if isActiveAdmin(request.auth.uid);

      // Only active admins can update payments
      allow update: if isActiveAdmin(request.auth.uid);
      
      // No one can delete payments
      allow delete: if false;
    }

    // --------------------------
    // Trips collection
    // --------------------------
    match /trips/{tripId} {
      // Helper function to check if user is a confirmed driver
      function isConfirmedDriver(uid) {
        let driverDoc = get(/databases/$(database)/documents/mobile_users/$(uid)).data;
        return driverDoc != null 
          && driverDoc.isDriverConfirmed != null 
          && driverDoc.isDriverConfirmed != '';
      }

      // Helper function to check if user is the chosen driver for this trip
      function isChosenDriver() {
        let trip = resource.data;
        return trip.uidChosenDriver == request.auth.uid 
          && isConfirmedDriver(request.auth.uid);
      }

      // Helper function to check if trip can be deleted
      function canDeleteTrip() {
        let trip = resource.data;
        return (trip.status == 'preparing' || trip.status == 'requestingDriver') && (
          // Either the user is the passenger
          request.auth.uid == trip.uidPassenger ||
          // Or an active admin
          isActiveAdmin(request.auth.uid)
        );
      }

      // Helper function to check if trip is in a non-final state
      function isNotPaidTrip() {
        let trip = resource.data;
        return trip.status != 'paid';
      }

      // Helper function to check if user is querying their ongoing trips
      function isQueryingOwnOngoingTrips() {
        return request.auth != null &&
               request.query.filters.hasAll([
                 ['uidPassenger', '==', request.auth.uid],
                 ['passengerDismissed', '==', false],
                 ['status', 'in', ['requestingDriver', 'driverApproaching', 'driverAwaiting', 'inProgress']]
               ]);
      }
      
      // Helper function to check if user is querying their trips with passengerDismissed filter
      function isQueryingOwnNonDismissedTrips() {
        return request.auth != null &&
               request.query.filters.hasAll([
                 ['uidPassenger', '==', request.auth.uid],
                 ['passengerDismissed', '==', false]
               ]);
      }

      // Allow read if user is:
      // - the passenger and the owner of the Trip
      // - the chosen driver (must be a confirmed driver)
      // - an active admin
      // - querying their own ongoing trips
      // - querying their own trips with passengerDismissed filter
      allow read: if !exists(/databases/$(database)/documents/trips/$(tripId)) ||
                    request.auth.uid == resource.data.uidPassenger || 
                    isChosenDriver() || 
                    isActiveAdmin(request.auth.uid) ||
                    isQueryingOwnOngoingTrips() ||
                    isQueryingOwnNonDismissedTrips();

      // Allow create if:
      // - user is authenticated AND
      // - user is setting themselves as the passenger
      allow create: if request.auth != null && 
                   request.resource.data.uidPassenger == request.auth.uid;

      // Updated update rule:
      // Allow update if:
      // - the user is an active admin OR
      // - the user is the passenger and either:
      //      - the trip is not paid, OR
      //      - the only changed field is 'passengerDismissed' being set to true
      // - OR the user is the chosen driver (for non-paid trips)
      allow update: if isActiveAdmin(request.auth.uid)
                    || (
                        request.auth.uid == resource.data.uidPassenger &&
                        (
                          isNotPaidTrip() || 
                          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['passengerDismissed'])
                        )
                    )
                    || (
                        isChosenDriver() && 
                        (
                          isNotPaidTrip() ||
                          request.resource.data.diff(resource.data).affectedKeys().hasOnly(['driverDismissed'])
                        )
                    );

      // Allow delete if:
      // - document doesn't exist OR
      // - trip is in 'preparing' status AND
      // - user is either the passenger or an active admin
      allow delete: if !exists(/databases/$(database)/documents/trips/$(tripId)) || canDeleteTrip();

      // --------------------------
      // Trip logs subcollection
      // --------------------------
      match /logs/{logId} {
        // Helper function to check if user is the trip's driver
        function isTripDriver() {
          let trip = get(/databases/$(database)/documents/trips/$(tripId)).data;
          return trip.uidChosenDriver == request.auth.uid;
        }

        // Helper function to check if user is the trip's passenger
        function isTripPassenger() {
          let trip = get(/databases/$(database)/documents/trips/$(tripId)).data;
          return trip.uidPassenger == request.auth.uid;
        }

        // Allow read if user is:
        // - the trip's driver
        // - the trip's passenger (needed for driver position tracking)
        // - an active admin
        allow read: if isTripDriver() || isTripPassenger() || isActiveAdmin(request.auth.uid);

        // Allow create if user is the trip's driver
        allow create: if isTripDriver();

        // No updates or deletes allowed for anyone
        allow update, delete: if !exists(/databases/$(database)/documents/trips/$(tripId)/logs/$(logId)) || false;
      }

      // --------------------------
      // Notification reminders subcollection
      // --------------------------
      match /notification_reminders/{reminderId} {
        // Only allow read access to active admins
        // This sub-collection is managed entirely by Cloud Functions
        allow read: if isActiveAdmin(request.auth.uid);

        // No write access for client applications
        // All writes are handled by Cloud Functions with admin privileges
        allow write: if false;
      }
    }

    // --------------------------
    // Event logs collection
    // --------------------------
    match /event_logs/{logId} {
      allow read: if isActiveAdmin(request.auth.uid);
      
      // Allow create if:
      // - user is authenticated AND
      // - uid field matches the authenticated user
      allow create: if request.auth != null && 
                   request.resource.data.uid == request.auth.uid;
                   
      allow update: if false;
      allow delete: if false;
    }

    // --------------------------
    // Route data collection
    // --------------------------
    match /route_data/{routeId} {
      // Helper function to check if user is involved in the trip
      function isInvolvedInTrip(tripId) {
        let trip = get(/databases/$(database)/documents/trips/$(tripId)).data;
        return trip != null && (
          request.auth.uid == trip.uidPassenger ||
          request.auth.uid == trip.uidChosenDriver
        );
      }

      // Allow read if:
      // - user is involved in the trip (passenger or driver)
      // - user is an active admin
      allow read: if isInvolvedInTrip(resource.data.tripId) ||
                    isActiveAdmin(request.auth.uid);

      // Allow write only from active admins
      // Note: Cloud Functions have admin privileges and bypass these rules
      allow write: if isActiveAdmin(request.auth.uid);
    }

    // --------------------------
    // Admin tenant access subcollection
    // --------------------------
    match /admin_users/{adminId}/tenants/{tenantId} {
      // Function to validate tenant role assignment
      function isValidTenantRole(tenantId, role) {
        // SUPER_ADMIN (role: 2) only allowed in 'fiaranow' tenant
        return role <= 1 || (role == 2 && tenantId == 'fiaranow');
      }

      // Allow admins to read their own tenant access
      allow read: if request.auth.uid == adminId;
      
      // Only super admins can manage tenant access with valid role
      allow write: if isSuperAdmin(request.auth.uid, 'fiaranow') 
        && isValidTenantRole(tenantId, request.resource.data.role);
    }

    // --------------------------
    // Tenants collection
    // --------------------------
    match /tenants/{tenantId} {
      // Allow read if user has tenant access
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Only super admins can manage tenants
      allow write: if isSuperAdmin(request.auth.uid, tenantId);
    }

    // --------------------------
    // Tenant-specific collections
    // --------------------------
    match /tenants/{tenantId}/{document=**} {
      // Default tenant access check for all tenant documents
      allow read, write: if hasActiveTenantAccess(request.auth.uid, tenantId);
    }

    // Specific rules for tenant collections
    match /tenants/{tenantId}/configurations/{configId} {
      // Allow authenticated users to read trip configuration and passenger notifications
      allow read: if request.auth != null && (
                     configId == 'tripConfiguration' || 
                     configId == 'passengerNotifications'
                   ) ||
                   hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Only admins with tenant access can write
      allow write: if hasActiveTenantAccess(request.auth.uid, tenantId) && hasMinimumRole(request.auth.uid, tenantId, 1);
    }

    match /tenants/{tenantId}/trips/{tripId} {
      // Helper function to check if user is involved in the trip
      function isTripParticipant() {
        let trip = resource.data;
        return request.auth.uid == trip.uidPassenger || 
               request.auth.uid == trip.uidChosenDriver;
      }

      // Helper function to check if trip can be deleted by passenger
      function canPassengerDeleteTrip() {
        let trip = resource.data;
        return (trip.status == 'preparing' || trip.status == 'requestingDriver') && 
               request.auth.uid == trip.uidPassenger;
      }

      // Allow read if user is participant or has tenant access
      allow read: if isTripParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Allow create if user is authenticated (for mobile app)
      allow create: if request.auth != null;
      
      // Allow update if user is participant or has tenant access
      allow update: if isTripParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Allow delete if:
      // - passenger can delete their own trip in preparing/requestingDriver status OR
      // - has tenant access with admin role
      allow delete: if canPassengerDeleteTrip() || 
                      (hasActiveTenantAccess(request.auth.uid, tenantId) && hasMinimumRole(request.auth.uid, tenantId, 1));

      // Trip logs subcollection
      match /logs/{logId} {
        function isTripDriver() {
          let trip = get(/databases/$(database)/documents/tenants/$(tenantId)/trips/$(tripId)).data;
          return trip.uidChosenDriver == request.auth.uid;
        }

        function isTripPassenger() {
          let trip = get(/databases/$(database)/documents/tenants/$(tenantId)/trips/$(tripId)).data;
          return trip.uidPassenger == request.auth.uid;
        }

        allow read: if isTripDriver() || isTripPassenger() || hasActiveTenantAccess(request.auth.uid, tenantId);
        allow create: if isTripDriver();
        allow update, delete: if false;
      }

      // Notification reminders subcollection
      match /notification_reminders/{reminderId} {
        allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
        allow write: if false; // Managed by Cloud Functions
      }
    }

    match /tenants/{tenantId}/payments/{paymentId} {
      function isPaymentParticipant() {
        let payment = resource.data;
        return request.auth.uid == payment.customerId || 
               request.auth.uid == payment.driverId;
      }

      allow read: if isPaymentParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
      allow write: if hasActiveTenantAccess(request.auth.uid, tenantId) && hasMinimumRole(request.auth.uid, tenantId, 1);
      allow delete: if false;
    }

    match /tenants/{tenantId}/feedbacks/{feedbackId} {
      // Allow users to create their own feedback
      allow create: if request.auth != null && request.resource.data.uid == request.auth.uid;
      
      // Allow users to read their own feedback or admins with tenant access
      allow read: if (request.auth.uid == resource.data.uid) || hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Only admins can update feedback
      allow update: if hasActiveTenantAccess(request.auth.uid, tenantId);
      
      allow delete: if false;
    }

    match /tenants/{tenantId}/chat_sessions/{sessionId} {
      function isParticipant() {
        return request.auth.uid in resource.data.participantUids;
      }

      allow read: if isParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
      allow create: if request.auth != null;
      allow update: if isParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
      allow delete: if false;

      // Chat messages subcollection
      match /chat_messages/{messageId} {
        function isSessionParticipant() {
          let session = get(/databases/$(database)/documents/tenants/$(tenantId)/chat_sessions/$(sessionId)).data;
          return request.auth.uid in session.participantUids;
        }

        allow read: if isSessionParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
        allow create: if isSessionParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
        allow update: if isSessionParticipant() || hasActiveTenantAccess(request.auth.uid, tenantId);
        allow delete: if false;
      }
    }

    match /tenants/{tenantId}/admin_notifications/{notificationId} {
      // Only admins with tenant access can read/write admin notifications
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      allow write: if hasActiveTenantAccess(request.auth.uid, tenantId);
    }

    match /tenants/{tenantId}/event_logs/{logId} {
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      allow create: if request.auth != null && request.resource.data.uid == request.auth.uid;
      allow update, delete: if false;
    }

    match /tenants/{tenantId}/route_data/{routeId} {
      function isInvolvedInTrip(tripId) {
        let trip = get(/databases/$(database)/documents/tenants/$(tenantId)/trips/$(tripId)).data;
        return trip != null && (
          request.auth.uid == trip.uidPassenger ||
          request.auth.uid == trip.uidChosenDriver
        );
      }

      allow read: if isInvolvedInTrip(resource.data.tripId) || hasActiveTenantAccess(request.auth.uid, tenantId);
      // Allow write only from active tenant admins
      // Note: Cloud Functions have admin privileges and bypass these rules
      allow write: if hasActiveTenantAccess(request.auth.uid, tenantId);
    }

    match /tenants/{tenantId}/feedback_events/{eventId} {
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      allow write: if false; // Managed by Cloud Functions
    }

    match /tenants/{tenantId}/feedback_statistics/{statId} {
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      allow write: if false; // Managed by Cloud Functions
    }

    // --------------------------
    // Driver Tag Management
    // --------------------------
    match /tenants/{tenantId}/driver_tags/{tagId} {
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId);
      allow write: if hasMinimumRole(request.auth.uid, tenantId, 1); // ADMIN required
    }

    // --------------------------
    // Driver Documents (Global with tenant access)
    // --------------------------
    match /mobile_users/{userId}/driver_documents/{docId} {
      // Owner can read/write their own documents
      allow read, write: if request.auth.uid == userId;

      // Active admins can read/write documents
      allow read, write: if isActiveAdmin(request.auth.uid);
    }

    // --------------------------
    // Global Vehicles Collection
    // --------------------------
    match /vehicles/{vehicleId} {
      // Allow read if:
      // - User is the owner of the vehicle OR
      // - User is the assigned driver OR
      // - User is authenticated (needed for driver lists) OR
      // - User is an admin
      allow read: if request.auth.uid == resource.data.ownerUID ||
                    request.auth.uid == resource.data.currentDriverId ||
                    request.auth != null ||
                    isActiveAdmin(request.auth.uid);
      
      // Allow write if user is the owner or an admin
      allow write: if request.auth.uid == resource.data.ownerUID ||
                     isActiveAdmin(request.auth.uid);
      
      // Any authenticated user can create a vehicle with themselves as owner
      allow create: if request.auth != null && 
                      request.resource.data.ownerUID == request.auth.uid;
      
      // --------------------------
      // Vehicle documents subcollection
      // --------------------------
      match /vehicle_documents/{docId} {
        // Allow vehicle owner or assigned driver to read documents
        allow read: if request.auth.uid == get(/databases/$(database)/documents/vehicles/$(vehicleId)).data.ownerUID ||
                      request.auth.uid == get(/databases/$(database)/documents/vehicles/$(vehicleId)).data.currentDriverId ||
                      isActiveAdmin(request.auth.uid);
        
        // Allow vehicle owner to create documents
        allow create: if request.auth.uid == get(/databases/$(database)/documents/vehicles/$(vehicleId)).data.ownerUID;
        
        // Allow vehicle owner or admin to update documents
        allow update: if request.auth.uid == get(/databases/$(database)/documents/vehicles/$(vehicleId)).data.ownerUID ||
                        isActiveAdmin(request.auth.uid);
        
        // No deletes allowed
        allow delete: if false;
      }
    }

    // --------------------------
    // Vehicle Tenant Linking
    // --------------------------
    match /tenants/{tenantId}/vehicles_linking/{linkingId} {
      // Allow read if:
      // - User has admin access to the tenant OR
      // - User is the owner of the linked vehicle OR
      // - User is authenticated (needed for driver vehicle info in lists)
      allow read: if hasActiveTenantAccess(request.auth.uid, tenantId) ||
                    (request.auth != null && exists(/databases/$(database)/documents/vehicles/$(resource.data.vehicleId)) && 
                     get(/databases/$(database)/documents/vehicles/$(resource.data.vehicleId)).data.ownerUID == request.auth.uid) ||
                    request.auth != null;
      
      // Only admins with ADMIN role can write
      allow write: if hasMinimumRole(request.auth.uid, tenantId, 1); // ADMIN required
    }

    // --------------------------
    // Mobile User Tenant States
    // --------------------------
    match /mobile_users/{userId}/tenant_states/{tenantId} {
      // Allow read if:
      // - User is reading their own tenant state OR
      // - User is an authenticated user (needed for driver vehicle info) OR
      // - User has admin access to the tenant
      allow read: if request.auth.uid == userId || 
                    request.auth != null ||
                    hasActiveTenantAccess(request.auth.uid, tenantId);
      
      // Allow users to write to their own tenant state OR admins with tenant access
      allow write: if request.auth.uid == userId || hasActiveTenantAccess(request.auth.uid, tenantId);
    }

    // --------------------------
    // Vehicle Assignments
    // --------------------------
    match /vehicle_assignments/{assignmentId} {
      allow read: if request.auth.uid == resource.data.driverUID ||
                    hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
      allow write: if hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
    }
  }
}