# Execution Plan: Mobile User Notifications Management System

## Overview

This task implements a comprehensive notification management system for the Flutter mobile app, similar to the existing admin notification system. Users will be able to view, manage, and interact with received push notifications from Firebase functions, with proper read/unread status tracking and contextual actions based on user type (driver vs passenger).

**Key Clarifications:**
- Only meaningful and relevant notifications will be stored (not all trip status changes)
- Notifications clicked by users are automatically marked as "read" and won't appear as unread
- Notifications will be placed in the top-right app bar (notifications icon with unread count + menu icon)

## Mobile Notifications Entity Relationship Diagram

```mermaid
erDiagram
    %% Firestore Collections - Core Data Model for Notifications
    TENANTS ||--o{ MOBILE_USER_NOTIFICATIONS : "contains"
    MOBILE_USERS ||--o{ MOBILE_USER_NOTIFICATIONS : "receives"
    TRIPS ||--o| MOBILE_USER_NOTIFICATIONS : "triggers"
    
    %% NEW: Mobile User Notifications Collection - Primary Storage
    %% Path: /tenants/{tenantId}/mobile_user_notifications/{notificationId}
    MOBILE_USER_NOTIFICATIONS {
        string id PK "auto-generated document ID"
        string recipientUID FK "references mobile_users.uid"
        string type "driver_moving | driver_arrived | trip_paid | reservation_reminder | driver_timeout | document_expiry | general"
        string title "notification display title"
        string body "notification message content"
        timestamp createdAt "serverTimestamp() when notification created"
        timestamp readAt "serverTimestamp() when user marked as read (null if unread)"
        boolean isRead "computed field: readAt != null"
        map data "structured payload for deep linking and context"
        string tripId "references trips.id (nullable for non-trip notifications)"
        string clickAction "app route for navigation (nullable)"
        string fcmMessageId "FCM message tracking ID (nullable)"
        boolean isDismissed "whether system notification was dismissed"
    }
    
    %% Global Collections - User and Tenant Context
    %% Path: /mobile_users/{uid}
    MOBILE_USERS {
        string uid PK "Firebase Auth UID"
        string displayName "user full name"
        string fcmToken "current FCM registration token (stored in document)"
        string primaryUserType "rider | driver"
        array tenantIDs "list of tenant IDs user belongs to"
    }
    
    %% Path: /tenants/{tenantId}
    TENANTS {
        string id PK "tenant identifier"
        string name "tenant display name"
        boolean isActive "operational status"
        timestamp createdAt "tenant creation date"
    }
    
    %% Tenant Collections - Trip Context for Notifications
    %% Path: /tenants/{tenantId}/trips/{tripId}
    TRIPS {
        string id PK "trip document ID"
        string uidPassenger FK "passenger mobile_users.uid"
        string uidChosenDriver FK "driver mobile_users.uid"
        string status "preparing | requestingDriver | driverApproaching | driverAwaiting | inProgress | completed | cancelled | paid | reserved"
        timestamp createdAt "trip request timestamp"
        map startLocation "pickup coordinates and address"
        map arrivalLocation "destination coordinates and address"
        number totalPrice "trip cost in Ariary"
    }
    
    %% Firestore Document References and Constraints
    MOBILE_USER_NOTIFICATIONS }|--|| MOBILE_USERS : recipientUID
    MOBILE_USER_NOTIFICATIONS }|--|| TENANTS : "tenant collection path"
    MOBILE_USER_NOTIFICATIONS }o--o| TRIPS : tripId
    TRIPS }|--|| TENANTS : "tenant collection path"
    TRIPS }|--|| MOBILE_USERS : uidPassenger
    TRIPS }|--|| MOBILE_USERS : uidChosenDriver
```

## Multi-Tenant Notification Architecture

```mermaid
graph TB
    subgraph "Firebase Functions (Backend)"
        NE[Notification Engine]
        NS[Notification Sender]
        FCM[Firebase Cloud Messaging]
    end
    
    subgraph "Tenant: fiaranow"
        TF["/tenants/fiaranow/mobile_user_notifications"]
        TT["/tenants/fiaranow/trips"]
        TA["/tenants/fiaranow/admin_notifications"]
    end
    
    subgraph "Tenant: company2"
        CF["/tenants/company2/mobile_user_notifications"]
        CT["/tenants/company2/trips"]
        CA["/tenants/company2/admin_notifications"]
    end
    
    subgraph "Global Collections"
        MU["/mobile_users"]
        MTS["/mobile_users/{uid}/tenant_states"]
    end
    
    subgraph "Flutter App (fiaranow)"
        NSS[NotificationStateService]
        NLS[NotificationsListScreen]
        NDS[NotificationDetailScreen]
        LN[Local Notifications]
    end
    
    subgraph "Flutter App (company2)"
        NSS2[NotificationStateService]
        NLS2[NotificationsListScreen] 
        NDS2[NotificationDetailScreen]
        LN2[Local Notifications]
    end
    
    %% Backend Flow
    NE --> NS
    NS --> FCM
    NS --> TF
    NS --> CF
    
    %% Mobile App Integration (fiaranow)
    FCM --> LN
    TF --> NSS
    NSS --> NLS
    NSS --> NDS
    MU --> NSS
    MTS --> NSS
    
    %% Mobile App Integration (company2)
    FCM --> LN2
    CF --> NSS2
    NSS2 --> NLS2
    NSS2 --> NDS2
    MU --> NSS2
    MTS --> NSS2
    
    %% Tenant Isolation
    TF -.-> TT
    CF -.-> CT
    TF -.-> MU
    CF -.-> MU
```

## Notification Flow Sequence Diagrams

### 1. Complete Notification Lifecycle (Trip-Related)

```mermaid
sequenceDiagram
    participant T as Trip Event
    participant FE as Firebase Functions
    participant FCM as Firebase Cloud Messaging
    participant FS as Firestore
    participant MA as Mobile App
    participant NS as NotificationStateService
    participant SN as System Notifications
    participant UI as Flutter UI

    %% Backend Notification Creation
    T->>+FE: Trip status changed (e.g., driver approaching)
    FE->>FE: Determine notification recipients
    FE->>FE: Check user preferences & tenant config
    
    par Parallel Operations
        FE->>+FS: Create MobileUserNotification document
        Note over FS: /tenants/{tenantId}/mobile_user_notifications/{id}
        FS-->>-FE: Document created
    and
        FE->>+FCM: Send push notification
        Note over FCM: FCM payload with custom data
        FCM-->>-FE: Message sent
    end
    
    FE-->>-T: Notification processing complete

    %% Mobile App Reception (Foreground)
    FCM->>+MA: Push message received (foreground)
    MA->>MA: Extract notification data
    MA->>+SN: Show local notification
    SN-->>-MA: Notification displayed
    MA->>+NS: Trigger Firestore listener refresh
    NS->>+FS: Query user notifications
    FS-->>-NS: Updated notification list
    NS->>NS: Update reactive observables
    NS-->>-MA: State updated

    %% UI Updates
    NS->>UI: Obx widgets rebuild
    UI->>UI: Show unread badge/indicator
    UI->>UI: Add notification to list

    %% User Interaction - Tap System Notification
    User->>+SN: Tap system notification
    SN->>+MA: App launched/resumed with payload
    MA->>MA: Parse deep link data
    
    alt Driver User & Trip Notification
        MA->>MA: Clear navigation stack
        MA->>UI: Navigate to MapScreen
        MA->>+SN: Dismiss system notification
        SN-->>-MA: Notification dismissed
    else Passenger User or Other Notification
        MA->>UI: Navigate to NotificationDetailScreen
        MA->>+SN: Dismiss system notification
        SN-->>-MA: Notification dismissed
    end

    %% Mark as Read Process
    UI->>+NS: Mark notification as read
    NS->>+FS: Update readAt timestamp & isRead flag
    FS-->>-NS: Document updated
    NS->>NS: Update local state
    NS->>UI: Update UI (remove unread indicator)
    NS-->>-UI: Read status confirmed
```

### 2. Background Notification Processing

```mermaid
sequenceDiagram
    participant FCM as Firebase Cloud Messaging
    participant BH as Background Handler
    participant WM as WorkManager
    participant FS as Firestore
    participant LN as Local Notifications
    participant MA as Mobile App

    %% Background Message Reception
    FCM->>+BH: Push message (app in background/killed)
    Note over BH: @pragma('vm:entry-point') handler
    
    BH->>BH: Minimal processing only
    BH->>+WM: Schedule notification work
    Note over WM: NotificationWorkManager.scheduleNotificationWork()
    WM-->>-BH: Work scheduled
    BH-->>-FCM: Background processing delegated

    %% WorkManager Processing
    WM->>+WM: Execute background task
    WM->>+FS: Initialize Firebase if needed
    FS-->>-WM: Firebase initialized
    
    WM->>WM: Reconstruct notification from input data
    WM->>WM: Determine notification type & priority
    
    alt High Priority (Driver Trip Request)
        WM->>+LN: Show urgent notification with ringtone
        Note over LN: Driver channel, max importance, sound
        LN-->>-WM: Urgent notification shown
    else Standard Priority (Trip Updates)
        WM->>+LN: Show standard notification
        Note over LN: Passenger channel, high importance
        LN-->>-WM: Standard notification shown
    else Silent Notification (Trip Paid)
        WM->>+LN: Show silent notification
        Note over LN: Silent channel, no sound/vibration
        LN-->>-WM: Silent notification shown
    end

    WM-->>-WM: Background task completed

    %% App Resume and Sync
    User->>+MA: Open app
    MA->>+NS: Initialize NotificationStateService
    NS->>+FS: Query latest notifications
    FS-->>-NS: Notification list with read statuses
    NS->>NS: Sync with local state
    NS-->>-MA: State service ready
    MA-->>-User: App ready with updated notifications
```

### 3. Notification List Management (GetX State)

```mermaid
sequenceDiagram
    participant U as User
    participant NLS as NotificationsListScreen
    participant NS as NotificationStateService
    participant FS as Firestore

    %% Screen Initialization
    U->>NLS: Navigate to notifications list
    NLS->>NS: Access notifications.value (RxList)
    
    alt First Time Load
        NS->>FS: Query collection with listener
        Note over FS: /tenants/{tenantId}/mobile_user_notifications<br/>where recipientUID == currentUser.uid<br/>orderBy createdAt desc
        FS-->>NS: Initial QuerySnapshot received
        NS->>NS: Convert docs to List<MobileUserNotification>
        NS->>NS: notifications.value = notificationList
        NS->>NS: unreadCount.value = count where isRead == false
        NS-->>NLS: Reactive state updated
        NLS-->>U: Display notifications list
    else Service Already Initialized
        NS-->>NLS: Return existing notifications.value
        NLS-->>U: Display cached list immediately
    end

    %% Real-time Updates from Firestore
    loop Firestore Real-time Updates
        FS->>NS: Document change detected
        NS->>NS: Process document changes
        NS->>NS: Update notifications.value list
        NS->>NS: Recalculate unreadCount.value
        NS->>NLS: Obx detects notifications change
        NLS->>NLS: Rebuild list widgets automatically
        NLS->>U: UI reflects new state
    end

    %% User Interactions
    U->>NLS: Tap notification item
    NLS->>NS: markAsReadAndNavigate(notificationId)
    NS->>FS: Update document: readAt = serverTimestamp()
    Note over FS: Document update triggers listener
    FS-->>NS: Update confirmation
    NS->>NS: Navigate to detail screen
    NS-->>NLS: Navigation completed
    NLS-->>U: Show detail screen

    %% Search Functionality
    U->>NLS: Enter search query
    NLS->>NS: Set searchQuery.value = "query"
    NS->>NS: Computed filteredNotifications updates
    Note over NS: filteredNotifications = notifications.where(matches query)
    NS->>NLS: Obx rebuilds with filtered results
    NLS-->>U: Display filtered list
```

### 4. Driver-Specific Navigation Flow

```mermaid
sequenceDiagram
    participant FCM as Firebase Cloud Messaging
    participant MA as Mobile App
    participant AS as AuthState
    participant NS as NavigationState
    participant MS as MapScreen
    participant NDS as NotificationDetailScreen
    participant SN as System Notifications

    %% Notification Reception
    FCM->>+MA: Trip request notification received
    MA->>MA: Parse notification payload
    MA->>+AS: Check user type
    AS-->>-MA: User is driver

    %% System Notification Tap
    User->>+SN: Tap trip notification from system tray
    SN->>+MA: Launch app with notification data
    MA->>MA: Extract deep link payload
    
    MA->>+AS: Verify user is driver
    AS-->>-MA: Confirmed driver user
    
    MA->>MA: Check notification type
    
    alt Trip-Related Notification (Driver)
        Note over MA: Special handling for drivers
        MA->>+NS: Clear navigation stack
        NS->>NS: Pop all routes to root
        NS-->>-MA: Navigation cleared
        
        MA->>+NS: Navigate to MapScreen
        NS->>+MS: Load with trip context
        MS->>MS: Show trip request on map
        MS->>MS: Display trip details overlay
        MS-->>-NS: Map ready with trip data
        NS-->>-MA: Navigation completed
        
        MA->>+SN: Dismiss system notification
        SN-->>-MA: Notification dismissed
        
        Note over MA: Driver sees trip immediately, no detail screen
        
    else Non-Trip Notification (Driver)
        MA->>+NS: Navigate to NotificationDetailScreen
        NS->>+NDS: Load notification details
        NDS->>NDS: Mark as read
        NDS->>NDS: Display full content
        NDS-->>-NS: Detail screen ready
        NS-->>-MA: Navigation completed
        
        MA->>+SN: Dismiss system notification
        SN-->>-MA: Notification dismissed
    end

    MA-->>-User: App ready with appropriate screen
```

### 5. Read Status Synchronization

```mermaid
sequenceDiagram
    participant U as User
    participant NDS as NotificationDetailScreen
    participant NS as NotificationStateService
    participant FS as Firestore
    participant RT as Realtime Listener
    participant OS as Other Screens/Devices

    %% Detail Screen Opens
    U->>+NDS: Open notification detail
    NDS->>+NS: Get notification by ID
    NS-->>-NDS: Return notification data
    
    NDS->>NDS: Check if already read
    
    alt Notification Unread
        NDS->>+NS: Mark as read
        NS->>+FS: Update document
        Note over FS: Set readAt: serverTimestamp(), isRead: true
        FS-->>-NS: Document updated
        
        %% Local State Update
        NS->>NS: Update RxList item locally
        NS->>NS: Decrement unread count
        NS->>NDS: Obx widgets rebuild
        NDS->>NDS: Update read indicator
        
        %% Real-time Sync to Other Instances
        FS->>+RT: Document change detected
        RT->>+OS: Broadcast to other listeners
        OS->>OS: Update read status in lists
        OS->>OS: Update unread badges
        OS-->>-RT: UI updated across instances
        RT-->>-FS: Sync completed
        
        NS-->>-NDS: Mark as read completed
    else Already Read
        NDS->>NDS: Display as read (no action needed)
    end

    %% Content Display
    NDS->>NDS: Show notification title & body
    NDS->>NDS: Display creation timestamp
    
    alt Has Click Action
        NDS->>NDS: Show "View Details" button
        U->>NDS: Tap action button
        NDS->>+NS: Navigate to related content
        NS-->>-NDS: Navigation completed
    end

    NDS-->>-U: Detail screen fully loaded

    %% Background Sync (Other Device/Session)
    Note over FS,RT: If user has multiple devices/sessions
    FS->>RT: Real-time update propagated
    RT->>OS: Update read status everywhere
    OS->>OS: Sync unread counts across devices
```

### 6. Error Handling and Fallback Flows

```mermaid
sequenceDiagram
    participant FCM as Firebase Cloud Messaging
    participant MA as Mobile App
    participant NS as NotificationStateService
    participant FS as Firestore
    participant LS as Local Storage
    participant U as User

    Note over FCM,U: Primary Flow with Firestore Error
    FCM->>MA: Push notification received
    MA->>NS: Process FCM notification
    NS->>FS: Query notification collection
    FS->>NS: Error: FirebaseException (network/permission)
    
    Note over NS,LS: Error Handling Strategy
    NS->>NS: Log error for debugging
    NS->>LS: Attempt to use cached data
    LS->>NS: Return cached notifications (if any)
    
    alt Has Cached Data
        NS->>NS: notifications.value = cachedList
        NS->>NS: isLoading.value = false
        NS->>NS: hasError.value = true
        NS->>U: Show cached list with "Offline" banner
    else No Cached Data Available
        NS->>NS: notifications.value = []
        NS->>NS: isLoading.value = false
        NS->>NS: hasError.value = true
        NS->>U: Show "Unable to load notifications" message
    end
    
    NS->>MA: Error handling completed

    Note over NS,FS: Retry Mechanism with Exponential Backoff
    NS->>NS: Start retry timer (initial: 2 seconds)
    
    loop Retry Attempts (Max 5 times)
        Note over NS: Timer expires, attempt retry
        NS->>FS: Retry query with timeout
        FS->>NS: Response (success or failure)
        
        alt Query Succeeds
            NS->>NS: notifications.value = fresh data
            NS->>NS: hasError.value = false
            NS->>LS: Cache fresh data locally
            LS->>NS: Data cached successfully
            NS->>U: Show fresh data, remove offline banner
            Note over NS: Retry successful - exit loop
        else Query Fails Again
            NS->>NS: Increase backoff: delay *= 2
            Note over NS: Next retry in 4s, then 8s, 16s, 32s
        end
    end
    
    Note over NS,U: Max Retries Reached
    NS->>NS: hasError.value = true (permanent)
    NS->>U: Display "Tap to retry" button

    Note over U,FS: Manual Retry by User
    U->>NS: Tap "Retry" button
    NS->>NS: Reset error state
    NS->>NS: isLoading.value = true
    NS->>FS: Fresh query attempt
    FS->>NS: Query response
    
    alt Manual Success
        NS->>NS: Process successful response
        NS->>U: Show fresh notifications
    else Manual Failure
        NS->>NS: Show persistent error state
        NS->>U: Show "Check connection" message
    end
    NS->>U: Manual retry completed

    Note over MA,FS: Network Connectivity Changes
    MA->>MA: Network connectivity restored
    MA->>NS: onConnectivityChanged(connected: true)
    
    alt Has Pending Changes
        NS->>FS: Sync pending read status updates
        Note over FS: Batch update readAt timestamps
        FS->>NS: Pending changes applied
        NS->>NS: Clear pending changes queue
    end
    
    NS->>FS: Refresh notification data
    FS->>NS: Latest data received
    NS->>NS: Merge with local state
    Note over NS: Server data wins for conflicts
    NS->>NS: hasError.value = false
    NS->>U: Show updated notifications without errors
    NS->>MA: Sync completed
```

## Analysis

### Current State

The existing codebase has:
- Firebase Cloud Messaging (FCM) infrastructure in place with `firebase/functions/src/notification_engine.ts`
- Notification handling services: `NotificationWorkManager.dart` and `NotificationSettingsScreen.dart`
- Admin notification system implemented in SvelteKit with list/detail views
- GetX state management pattern used throughout the Flutter app
- Push notification configuration and permissions handling
- Firebase Firestore collections for notification storage in admin section
- Current app bar structure with menu icon on the right side

**Driver Timeout Implementation Status:**
- ❌ Driver timeout notifications are NOT currently stored in mobile user notifications collection
- ❌ Passengers cannot view timeout notification history in the app

### Requirements

1. **Notification List Screen**: Display all received notifications with visual read/unread indicators
2. **Notification Detail Screen**: Show detailed notification content and mark as read
3. **Read Status Management**: Track and update read status per user (auto-mark as read when clicked)
4. **System Integration**: Dismiss Android/iOS notifications when opened from app
5. **Driver-Specific Behavior**: For drivers, trip notifications navigate to map instead of detail view
6. **Navigation Handling**: Handle notifications from system tray to open appropriate screens
7. **State Management**: Use GetX pattern for reactive UI updates
8. **Firestore Integration**: Store and sync notification data with Firebase
9. **App Bar Integration**: Add notifications icon with unread count next to menu in top-right app bar
10. **Selective Storage**: Only store meaningful notifications (not all trip status changes)

### Research Findings

- Flutter 2025 best practices recommend using WorkManager for background notification processing
- GetX .obs pattern with Obx widgets provides optimal performance for list updates
- Firebase Messaging supports custom data payloads for notification routing
- flutter_local_notifications can dismiss specific notifications by ID
- Firestore real-time listeners work well with GetX reactive state management

## Task List

### Task 1: Create Notification Model and Firebase Collection Structure

- **Objective**: Define the data structure for mobile user notifications
- **Actions**: 
  - Create `MobileUserNotification.dart` model class
  - Define Firestore collection structure for user notifications
  - Add notification type enums and helper methods
- **Files**: 
  - `fiaranow_flutter/lib/models/MobileUserNotification.dart`
  - Update Firebase Functions to create user notification documents
- **Validation**: Model can serialize/deserialize from Firestore, all required fields present

### Task 2: Implement Notification State Management Service

- **Objective**: Create GetX service for managing notification state and Firestore operations
- **Actions**:
  - Create `NotificationStateService` extending GetXService
  - Implement real-time Firestore listeners for user notifications
  - Add methods for marking notifications as read
  - Handle notification dismissal and status updates
- **Files**: 
  - `fiaranow_flutter/lib/services/NotificationStateService.dart`
  - Register service in main.dart dependency injection
- **Validation**: Service loads notifications, updates read status, syncs with Firestore

### Task 3: Create Notifications List Screen

- **Objective**: Build the main notifications list UI with read/unread indicators
- **Actions**:
  - Create `NotificationsListScreen` with GetX Obx widgets
  - Implement search and filter functionality
  - Add visual indicators for read/unread status
  - Create list item components with proper tap handling
- **Files**: 
  - `fiaranow_flutter/lib/screens/NotificationsListScreen.dart`
  - `fiaranow_flutter/lib/widgets/NotificationListItem.dart`
- **Validation**: Screen displays notifications list, shows read/unread status, handles taps

### Task 4: Implement Notification Detail Screen

- **Objective**: Create detailed notification view with auto-mark-as-read functionality
- **Actions**:
  - Create `NotificationDetailScreen` with full notification content
  - Implement automatic read status marking on screen open
  - Add contextual actions based on notification type
  - Handle deep linking and navigation from notification data
- **Files**: 
  - `fiaranow_flutter/lib/screens/NotificationDetailScreen.dart`
- **Validation**: Detail screen shows full content, marks as read, supports navigation actions

### Task 5: Integrate System Notification Handling

- **Objective**: Handle notifications from Android/iOS system tray and dismiss them appropriately
- **Actions**:
  - Extend FCM handling to support notification routing
  - Implement notification dismissal when opened from app
  - Add deep linking support for notification navigation
  - Update `NotificationWorkManager` to handle routing data
- **Files**: 
  - Update `fiaranow_flutter/lib/services/NotificationWorkManager.dart`
  - Update `fiaranow_flutter/lib/fcm.dart`
  - Update main.dart notification handling
- **Validation**: System notifications dismiss when opened, deep linking works correctly

### Task 6: Implement Driver-Specific Navigation Logic

- **Objective**: Handle special navigation behavior for driver users receiving trip notifications
- **Actions**:
  - Add user type detection to notification handling
  - Implement route clearing and map navigation for drivers
  - Ensure passenger notifications open detail screen normally
  - Add notification type-specific routing logic
- **Files**: 
  - Update notification routing logic in services
  - Modify navigation handling based on user role
- **Validation**: Drivers navigate to map for trip notifications, passengers see detail screen

### Task 7: Update Firebase Functions for Mobile Notifications

- **Objective**: Modify backend functions to create mobile user notification documents for meaningful notifications only
- **Actions**:
  - Update notification sending functions to create Firestore documents
  - Add mobile notification collection creation for relevant notification types only
  - Ensure notification data includes routing information
  - Verify driver timeout notifications create mobile notification documents
  - Test notification delivery to mobile collection
- **Files**:
  - Update `firebase/functions/src/notification_engine.ts`
  - Update `firebase/functions/src/notifications.ts`
  - Update `firebase/functions/src/driver_request_timeout.ts` if needed
- **Validation**: Functions create mobile notification documents for meaningful notifications, routing data is included

### Task 8: Add App Bar Notifications Integration

- **Objective**: Integrate notifications icon with unread count into the top-right app bar
- **Actions**:
  - Add routes for notifications list and detail screens
  - Update MainPage app bar to include notifications icon with badge
  - Position notifications icon to the left of the existing menu icon
  - Implement unread count badge display
  - Handle navigation state management with GetX
- **Files**:
  - Update main.dart routing
  - Update `fiaranow_flutter/lib/MainPage.dart` app bar actions
  - Update `fiaranow_flutter/lib/states/NavigationState.dart` if needed
- **Validation**: Notifications icon appears in app bar with correct unread count, routing works correctly

### Task 9: Implement Notification Badge and Counters

- **Objective**: Add visual indicators for unread notification count in app bar
- **Actions**:
  - Add unread count reactive variables to state service
  - Implement badge display on app bar notifications icon
  - Add counter updates when notifications are read/clicked
  - Ensure real-time updates across app
  - Auto-mark notifications as read when clicked from system tray or app
- **Files**:
  - Update `fiaranow_flutter/lib/services/NotificationStateService.dart`
  - Update app bar notification icon with badge
- **Validation**: Unread count displays correctly, updates in real-time, auto-marks as read on click

### Task 10: Handle Deep Linking and Navigation

- **Objective**: Implement proper navigation from notifications to relevant screens with auto-read marking
- **Actions**:
  - Add deep linking support for notification actions
  - Implement navigation logic for different notification types
  - Handle navigation from system notifications to app screens
  - Add proper state restoration when navigating from notifications
  - Auto-mark notifications as read when clicked from system tray
  - Implement driver-specific navigation (trip notifications → map screen)
- **Files**:
  - Update notification handling in `fiaranow_flutter/lib/fcm.dart`
  - Update main app routing and navigation logic
- **Validation**: Notifications navigate to correct screens, auto-mark as read, driver navigation works correctly

### Task 11: Add Localization and Error Handling

- **Objective**: Add proper error handling and localized text strings for notifications
- **Actions**:
  - Add comprehensive notification-related strings to localization files (list, detail, empty states, errors)
  - Implement error handling for network failures and Firestore issues
  - Add loading states and empty state UI
  - Test offline behavior and sync when reconnected
  - Follow existing ARB file patterns and sync process
- **Files**:
  - Update `fiaranow_flutter/lib/l10n/app_en.arb` (template file)
  - Update `fiaranow_flutter/lib/l10n/app_fr.arb` using sync script
  - Add error handling to all notification services and screens
- **Validation**: All text is localized, errors handled gracefully, offline behavior works

### Task 12: Testing and Integration Validation

- **Objective**: Comprehensive testing of notification system functionality
- **Actions**:
  - Test notification delivery from Firebase functions
  - Verify read/unread status synchronization
  - Test driver vs passenger navigation behavior
  - Validate system notification dismissal
  - Test deep linking and routing from notifications
- **Files**: 
  - All notification-related files
  - Test notification delivery pipeline
- **Validation**: All features work as specified, edge cases handled properly

### Task 13: Performance Optimization and Code Analysis

- **Objective**: Ensure optimal performance and run code quality checks
- **Actions**:
  - Run `fvm flutter analyze` to check for issues
  - Optimize Firestore queries and listeners
  - Verify memory usage and dispose patterns
  - Test with large notification datasets
- **Files**: 
  - All notification implementation files
- **Validation**: No analyzer warnings, good performance with large datasets

### Task 14: Update Documentation and Reference Models

- **Objective**: Update project documentation to reflect the new mobile user notifications collection
- **Actions**:
  - Add `mobile_user_notifications` collection to `MODELS.md`
  - Update tenant-specific collections list in `MULTI-TENANT.md`
  - Document the notification data model with proper field definitions
  - Add notification types to enum definitions
  - Update entity relationship diagram in MODELS.md to include notification relationships
- **Files**: 
  - `MODELS.md` - Add MobileUserNotification model definition
  - `MULTI-TENANT.md` - Add mobile_user_notifications to tenant collections
- **Validation**: Documentation accurately reflects the new notification system architecture

**Important Note**: The current entity diagram in this plan is consistent with both MODELS.md and MULTI-TENANT.md:
- Uses proper multi-tenant collection path: `/tenants/{tenantId}/mobile_user_notifications/`
- Follows the established pattern of tenant-specific collections for operational data
- References global collections (mobile_users, tenants, vehicles) appropriately
- Maintains proper foreign key relationships with existing entities