# Multi-Tenant Architecture Guide

**IMPORTANT: If tenant feature changes, this document must be updated accordingly**

## Overview

Fiaranow uses a multi-tenant architecture where multiple taxi companies can operate independently within the same Firebase project. Each tenant has isolated data while sharing common infrastructure.

## Data Architecture

### Global Collections (Shared Across All Tenants)

These collections remain at the root level and are shared:

```
/admin_users/{uid}
/mobile_users/{uid}
/tenants/{tenantId}
/global_configurations/{configId}
/vehicles/{vehicleId}
/vehicle_assignments/{assignmentId}
```

**Why Global:**
- `admin_users`: <PERSON><PERSON> can manage multiple tenants
- `mobile_users`: Users (drivers/passengers) can potentially work across tenants
- `tenants`: Registry of all tenant organizations
- `global_configurations`: App-wide settings (version requirements, maintenance)
- `vehicles`: Vehicles can be tenant-owned or user-owned, need global access for cross-tenant scenarios
- `vehicle_assignments`: Assignment history tracking across all tenants

### Tenant-Specific Collections

These collections are isolated per tenant under `/tenants/{tenantId}/`:

```
/tenants/{tenantId}/configurations/{configId}
/tenants/{tenantId}/trips/{tripId}
/tenants/{tenantId}/payments/{paymentId}
/tenants/{tenantId}/event_logs/{logId}
/tenants/{tenantId}/route_data/{routeId}
/tenants/{tenantId}/feedbacks/{feedbackId}
/tenants/{tenantId}/feedback_events/{eventId}
/tenants/{tenantId}/feedback_statistics/{statId}
/tenants/{tenantId}/chat_sessions/{sessionId}
/tenants/{tenantId}/chat_messages/{messageId}
/tenants/{tenantId}/admin_notifications/{notificationId}
/tenants/{tenantId}/driver_tags/{tagId}
/tenants/{tenantId}/vehicles_linking/{linkingId}
```

**Why Tenant-Specific:**
- Business operations data (trips, payments)
- Customer interactions (feedback, chat)
- Operational configurations (pricing, settings)
- Analytics and reporting data
- Driver management (tags, vehicle associations)
- Vehicle-tenant relationships and approvals

## Tenant Management

### Tenant Document Structure

```typescript
// /tenants/{tenantId}
interface Tenant {
  id: string;           // "fiaranow", "company2", etc.
  name: string;         // "Fiaranow", "City Taxi Co."
  isActive: boolean;    // Can the tenant operate?
  createdAt: Timestamp;
  settings: {
    // Tenant-specific configurations
    branding?: {
      primaryColor: string;
      logo: string;
    };
    features?: {
      enableChat: boolean;
      enableFeedback: boolean;
    };
  };
}
```

### Admin Access Control

```typescript
// /admin_users/{uid} - Base admin user document
interface AdminUser {
  uid: string;
  email: string;
  displayName: string;
  isActive: boolean;
  // No role or tenantAccess fields here - they are per-tenant
}

// /admin_users/{uid}/tenants/{tenantId} - Per-tenant access
interface TenantAccess {
  tenantId: string;
  isActive: boolean;
  role: number;         // 0=MANAGER, 1=ADMIN, 2=SUPER_ADMIN
  assignedAt: Date;
  assignedBy: string;   // UID of admin who granted access
}

// /mobile_users/{uid} - Base mobile user document
interface MobileUser {
  uid: string;
  email: string;
  displayName: string;
  // ... other fields
  tenantIDs: string[];  // Array of tenant IDs for performance optimization
  occupiedByTripId?: string; // EXISTING: Indicates driver is currently occupied with this specific Trip ID
  isServiceActiveByTenant?: { [tenantId: string]: boolean }; // DENORMALIZED: Service status per tenant, synced from MobileUserTenantState
}

// /mobile_users/{uid}/tenant_states/{tenantId} - Per-tenant state
interface MobileUserTenantState {
  tenantId: string;
  isActive: boolean;
  currentVehicleLinkingId?: string; // Currently assigned vehicle linking in this tenant
  driverTags: string[];        // Tags assigned in this tenant
  isDriverConfirmed?: string;  // Admin UID who confirmed driver in this tenant
  isServiceActive: boolean;    // SOURCE OF TRUTH: Service status in this tenant, synced to MobileUser.isServiceActiveByTenant
  joinedAt: Date;
  lastActiveAt: Date;
}
```

**Role Hierarchy (Per Tenant):**
- **SUPER_ADMIN (2)**: Full access to tenant, can manage other admins
  - ⚠️ **RESTRICTION**: Only available in the `fiaranow` tenant
  - SUPER_ADMINs can access and manage all other tenants
- **ADMIN (1)**: Full operational access to tenant, vehicle approval
- **MANAGER (0)**: Limited access (trips only) to tenant

**Key Principles**:
- An admin can have different roles across different tenants
- SUPER_ADMIN role is exclusive to the `fiaranow` tenant
- All other tenants only use ADMIN (1) and MANAGER (0) roles

## Vehicle Management Architecture

**IMPORTANT**: As of Sprint #7, the system has migrated from the legacy `driverProfile` field to a comprehensive vehicle management system. The `driverProfile` field has been completely removed from all `mobile_users` documents.

### Global Vehicle Collection

```typescript
// /vehicles/{vehicleId} - Global vehicle collection (base data)
interface Vehicle {
  id: string;
  brand: string;
  model: string;
  color: string;
  year: number;
  registrationNumber: string;
  maxPassengers: number;    // CRITICAL: This is the source of truth for capacity
  isActive: boolean;
  ownerUID?: string;        // For user-owned vehicles (external drivers)

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;        // Admin UID or mobile user UID
}

// /tenants/{tenantId}/vehicles_linking/{linkingId} - Tenant-vehicle associations
interface VehicleLinking {
  id: string;               // linkingId
  vehicleId: string;        // Reference to global vehicle
  tenantId: string;         // Current tenant

  // Tenant-specific vehicle state
  tenantRemark?: string;    // Admin notes about the vehicle
  tenantApproved?: boolean; // Whether tenant has approved this vehicle for use
  currentDriverId?: string; // Who is currently driving this vehicle in this tenant

  // Linking metadata
  linkedAt: Date;
  linkedBy: string;         // Admin UID who linked the vehicle
  isActive: boolean;        // Whether this linking is active

  // For tenant-owned vehicles
  isOwnedByTenant: boolean; // true for internal fleet, false for external vehicles
}
```

### Vehicle Management: Two-Layer System

The vehicle management system uses a **two-layer architecture** to separate vehicle availability from assignment history:

#### Layer 1: Vehicle-Tenant Linking (`vehicles_linking`)
**Purpose**: Manages which vehicles are **available** to which tenants and their approval status.

**Tenant-Owned Vehicles (Internal Fleet):**
- `isOwnedByTenant: true` in vehicle linking
- Vehicle created by tenant admin
- Managed by tenant admins
- Can only be assigned to drivers within the tenant
- No approval process required

**User-Owned Vehicles (External Drivers):**
- `isOwnedByTenant: false` in vehicle linking
- Vehicle created by mobile driver (`ownerUID` set in global vehicle)
- Linked to tenant via vehicle linking collection
- Requires tenant approval (`tenantApproved: true`) before use
- Can be linked to multiple tenants with separate approval status

#### Layer 2: Assignment History (`vehicle_assignments`)

**Purpose**: Maintains a **historical audit trail** of all vehicle assignments across all tenants.

```typescript
// /vehicle_assignments/{assignmentId} - Global assignment tracking
interface VehicleAssignment {
  id: string;
  vehicleId: string;
  vehicleLinkingId: string;    // Reference to tenant vehicle linking
  driverUID: string;
  tenantId: string;            // Always present now (from linking)

  createdAt: Date;             // For efficient sorting
  assignedAt: Date;
  assignedBy: string;          // Admin UID or 'system'
  unassignedAt?: Date;
  unassignedBy?: string;

  reason: 'admin_assignment' | 'driver_switch' | 'vehicle_maintenance' | 'driver_unavailable';
  notes?: string;
  isActive: boolean;           // Current assignment
}
```

### Key Distinctions Between the Two Layers

| Aspect | `vehicles_linking` (Layer 1) | `vehicle_assignments` (Layer 2) |
|--------|------------------------------|----------------------------------|
| **Scope** | Tenant-specific | Global across all tenants |
| **Purpose** | Vehicle-tenant relationship & approval | Assignment history & audit trail |
| **Data Type** | Current state | Historical records |
| **Lifecycle** | Long-lived (while vehicle linked to tenant) | Multiple records per vehicle over time |
| **Primary Question** | "Can this vehicle be used in this tenant?" | "Who has used this vehicle and when?" |
| **Current Assignment** | `currentDriverId` field | `isActive: true` record |
| **Approval Status** | `tenantApproved` field | Not applicable |
| **Multi-Tenant** | One record per tenant-vehicle pair | One record per assignment event |

**Example Workflow**:
1. **Vehicle Linking**: Driver registers vehicle → Admin approves via `vehicles_linking` → Vehicle becomes available to tenant
2. **Assignment**: Admin assigns approved vehicle to driver → New `vehicle_assignments` record created → `vehicles_linking.currentDriverId` updated
3. **Reassignment**: Admin assigns same vehicle to different driver → Previous `vehicle_assignments` record marked inactive → New active record created

### Migration from DriverProfile (since Sprint #7)

**BREAKING CHANGE**: The legacy `driverProfile` field has been completely removed from the `MobileUser` interface and all related code. This field previously stored vehicle information directly in the user document, which caused issues with multi-tenant vehicle management.

**What Changed**:
- ❌ **Removed**: `driverProfile` field from `MobileUser` documents
- ❌ **Removed**: `DriverProfile` interface and related forms
- ✅ **Added**: Comprehensive two-layer vehicle management system with proper tenant isolation
- ✅ **Added**: Vehicle linking system (`vehicles_linking`) for tenant-specific vehicle availability
- ✅ **Added**: Vehicle assignment history tracking (`vehicle_assignments`) for audit trails

**Migration Path**:
1. All existing `driverProfile` data was migrated to the new two-layer vehicle system
2. Vehicle information is now stored in global `/vehicles/{vehicleId}` collection
3. Tenant-specific vehicle availability uses `/tenants/{tenantId}/vehicles_linking/{linkingId}`
4. Assignment history is tracked in global `/vehicle_assignments/{assignmentId}` collection
5. Driver-vehicle relationships are managed through `MobileUserTenantState.currentVehicleLinkingId`

**Capacity Management**:
- **Before**: Hardcoded or stored in `driverProfile.maxPassengers`
- **After**: Retrieved from `Vehicle.maxPassengers` via the linking system
- **Critical**: Never use hardcoded capacity values - always query the actual vehicle data

### Driver-Tenant State Management

Mobile users can work across multiple tenants with different states per tenant:

```typescript
// Performance optimization: tenantIDs array in MobileUser
interface MobileUser {
  // ... existing fields
  tenantIDs: string[];  // Array of tenant IDs for fast queries
  occupiedByTripId?: string; // EXISTING: Indicates driver is currently occupied with this specific Trip ID
  isServiceActiveByTenant?: { [tenantId: string]: boolean }; // DENORMALIZED: Service status per tenant for efficient querying
}

// Detailed state per tenant
// /mobile_users/{uid}/tenant_states/{tenantId}
interface MobileUserTenantState {
  uid: string;                 // User UID for grouped collection queries
  tenantId: string;
  isActive: boolean;
  currentVehicleLinkingId?: string; // Currently assigned vehicle linking in this tenant
  driverTags: string[];        // Tags assigned in this tenant
  isDriverConfirmed?: string;  // Admin UID who confirmed driver in this tenant
  isServiceActive: boolean;    // SOURCE OF TRUTH: Service status in this tenant, automatically synced to MobileUser.isServiceActiveByTenant
  joinedAt: Date;
  lastActiveAt: Date;
}
```

## Implementation Patterns

### Mobile App (Flutter)

**Hardcoded Tenant**: Each mobile app build is tied to one tenant.

```dart
// lib/config/tenant_config.dart
class TenantConfig {
  static const String TENANT_ID = 'fiaranow'; // Hardcoded per build
  
  static String getTenantPath(String collection) {
    return 'tenants/$TENANT_ID/$collection';
  }
}

// Usage in models
final tripsColl = FirebaseFirestore.instance
  .collection(TenantConfig.getTenantPath('trips'))
  .withConverter<Trip>(...);
```

**Function Calls**: Always include tenant ID.

```dart
// All cloud function calls must include tenantId
final response = await callable.call({
  'tenantId': TenantConfig.TENANT_ID,
  'tripId': tripId,
  // ... other parameters
});
```

**Vehicle Capacity Retrieval**: Always get actual vehicle data.

```dart
// ✅ Correct: Get actual vehicle capacity
Future<int> getDriverVehicleCapacity(String driverUid) async {
  // Get driver's tenant state
  final tenantStateDoc = await MobileUserTenantState.getTenantStatesColl(driverUid)
      .doc(TenantConfig.TENANT_ID)
      .get();

  if (!tenantStateDoc.exists || tenantStateDoc.data()?.currentVehicleLinkingId == null) {
    return 4; // Default only if no vehicle assigned
  }

  final tenantState = tenantStateDoc.data()!;

  // Get vehicle linking
  final linkingDoc = await VehicleLinking.vehicleLinkingColl
      .doc(tenantState.currentVehicleLinkingId)
      .get();

  if (!linkingDoc.exists) return 4;

  final linking = linkingDoc.data()!;

  // Get actual vehicle capacity
  final vehicleDoc = await Vehicle.vehiclesColl.doc(linking.vehicleId).get();

  if (!vehicleDoc.exists) return 4;

  return vehicleDoc.data()!.maxPassengers; // Real capacity
}

// ❌ Wrong: Hardcoded capacity
const int driverCapacity = 4; // Never do this!
```

### Admin Panel (SvelteKit)

**Dynamic Tenant Selection**: Admins can switch between tenants they have access to.

```typescript
// lib/stores/tenant.svelte.ts
class TenantStore {
  currentId: string = 'fiaranow';
  availableTenants: Tenant[] = [];
  
  getTenantPath(collection: string) {
    return `tenants/${this.currentId}/${collection}`;
  }
}

// Usage in stores
const tripsRef = collection(fdb, tenantStore.getTenantPath('trips'));
```

### Firebase Functions

**Tenant-Aware Operations**: All functions accept and validate tenant ID.

```typescript
// Always use tenant utilities
import { getTenantCollection, ensureTenantId } from './tenant_utils';

export const someFunction = onCall(async (request) => {
  const { tenantId, ...otherData } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId); // Defaults to 'fiaranow'
  
  const tripsRef = getTenantCollection(effectiveTenantId, 'trips');
  // ... rest of function
});
```

**Event Triggers**: Include tenant ID in document paths.

```typescript
// Correct: Tenant-aware trigger
export const onTripCreated = onDocumentCreated({
  document: "/tenants/{tenantId}/trips/{tripId}",
  region: "europe-west3",
}, async (event) => {
  const tenantId = event.params.tenantId;
  const tripId = event.params.tripId;
  // ... handle event
});
```

## Security Rules

### Tenant Access Validation

```javascript
// firestore.rules
function getTenantAccess(uid, tenantId) {
  return get(/databases/$(database)/documents/admin_users/$(uid)/tenants/$(tenantId)).data;
}

function hasActiveTenantAccess(uid, tenantId) {
  let admin = getAdminUser(uid);
  let tenantAccess = getTenantAccess(uid, tenantId);
  return admin != null && admin.isActive == true &&
         tenantAccess != null && tenantAccess.isActive == true;
}

function hasMinimumTenantRole(uid, tenantId, minRole) {
  let tenantAccess = getTenantAccess(uid, tenantId);
  return tenantAccess != null && tenantAccess.isActive == true &&
         tenantAccess.role >= minRole;
}

function isSuperAdmin(uid) {
  // SUPER_ADMIN role only exists in 'fiaranow' tenant
  return hasMinimumTenantRole(uid, 'fiaranow', 2);
}

function canManageAllTenants(uid) {
  // Only SUPER_ADMINs from fiaranow tenant can manage other tenants
  return isSuperAdmin(uid);
}

// Tenant-specific collection rules
match /tenants/{tenantId}/trips/{tripId} {
  allow read, write: if hasActiveTenantAccess(request.auth.uid, tenantId);
}

// Global vehicle collection rules (base data only)
match /vehicles/{vehicleId} {
  // Owner can read/write their own vehicle
  allow read, write: if request.auth.uid == resource.data.ownerUID;

  // Any authenticated user can read vehicle base data (brand, model, etc.)
  allow read: if request.auth != null;

  // Only vehicle owner or admins can write
  allow write: if request.auth.uid == resource.data.ownerUID || isActiveAdmin(request.auth.uid);
}

// Tenant vehicle linking rules
match /tenants/{tenantId}/vehicles_linking/{linkingId} {
  // Tenant admins can read/write vehicle linking
  allow read, write: if hasActiveTenantAccess(request.auth.uid, tenantId);

  // Assigned driver can read linking details
  allow read: if request.auth.uid == resource.data.currentDriverId;
}

// Mobile user tenant states
match /mobile_users/{userId}/tenant_states/{tenantId} {
  allow read: if request.auth.uid == userId || hasActiveTenantAccess(request.auth.uid, tenantId);
  allow write: if hasActiveTenantAccess(request.auth.uid, tenantId);
}

// Vehicle assignment history
match /vehicle_assignments/{assignmentId} {
  allow read: if request.auth.uid == resource.data.driverUID ||
                 hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
  allow write: if hasActiveTenantAccess(request.auth.uid, resource.data.tenantId);
}
```

## Migration Strategy

### From Single-Tenant to Multi-Tenant

1. **Create Default Tenant**: Add `/tenants/fiaranow` document
2. **Migrate Data**: Move collections to `/tenants/fiaranow/`
3. **Update Admin Users**: Add `tenantAccess: ["fiaranow"]`
4. **Deploy Code**: Update all collection references
5. **Verify**: Test tenant isolation

### Adding New Tenants

1. **Create Tenant Document**: `/tenants/{newTenantId}`
2. **Assign Admins**: Create tenant access documents with ADMIN (1) or MANAGER (0) roles only
   - ⚠️ **No SUPER_ADMIN (2) roles** for non-fiaranow tenants
3. **Deploy Mobile App**: New build with different `TENANT_ID`
4. **Configure**: Set tenant-specific settings
5. **Validation**: Ensure no role > 1 is assigned to non-fiaranow tenants

## Development Guidelines

### DO's

✅ **Always use tenant utilities** in Firebase Functions
✅ **Include tenantId** in all function calls from mobile app
✅ **Use tenant-aware paths** in admin panel stores
✅ **Validate tenant access** in security rules
✅ **Test tenant isolation** thoroughly
✅ **Restrict SUPER_ADMIN** to fiaranow tenant only
✅ **Use transactional operations** for vehicle and tenant state changes
✅ **Update tenantIDs array** when mobile user tenant state changes
✅ **Validate vehicle ownership** before assignment operations
✅ **Understand the two-layer system**: Use `vehicles_linking` for availability, `vehicle_assignments` for history
✅ **Update both layers** when making assignments: linking current state + assignment history
✅ **Use denormalized service status** for efficient querying: `isServiceActiveByTenant` field
✅ **Update tenant state source of truth** for service status: `MobileUserTenantState.isServiceActive`
✅ **Ensure tenant state creation** during mobile app login for proper sync

### DON'Ts

❌ **Never hardcode collection paths** in functions
❌ **Don't forget tenant ID** in function parameters
❌ **Don't mix tenant data** in queries
❌ **Don't bypass tenant access checks**
❌ **Don't use global collections** for tenant-specific data
❌ **Don't assign SUPER_ADMIN role** to non-fiaranow tenants
❌ **Don't assign vehicles** without proper ownership validation
❌ **Don't forget to update** vehicle assignment history
❌ **Don't allow unapproved user vehicles** in tenant operations
❌ **NEVER use hardcoded vehicle capacity** - always retrieve from actual vehicle data
❌ **Don't reference driverProfile** - this field has been completely removed
❌ **Don't confuse the two layers**: `vehicles_linking` ≠ `vehicle_assignments`
❌ **Don't update only one layer** - both linking state and assignment history must be maintained
❌ **Don't manually update `isServiceActiveByTenant`** - it's automatically synced from tenant state
❌ **Don't use inefficient queries** for service status - use denormalized field instead of sub-collection queries
❌ **Don't bypass tenant state creation** in mobile app login flow

### Service Status Querying Pattern

```typescript
// ✅ Correct: Efficient driver filtering with denormalized data
export const getActiveDrivers = onCall(async (request) => {
  const { tenantId } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  // Single efficient query using denormalized field
  const activeDriversSnapshot = await db
    .collection('mobile_users')
    .where(`isServiceActiveByTenant.${effectiveTenantId}`, '==', true)
    .get();

  return activeDriversSnapshot.docs.map(doc => doc.data());
});

// ✅ Correct: Update service status (source of truth)
export const updateDriverServiceStatus = onCall(async (request) => {
  const { tenantId, driverUid, isActive } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  // Update the source of truth - sync function will handle denormalization
  const tenantStateRef = db
    .collection('mobile_users')
    .doc(driverUid)
    .collection('tenant_states')
    .doc(effectiveTenantId);

  await tenantStateRef.update({ isServiceActive: isActive });
  // onTenantStateChangeUpdateUser function automatically updates MobileUser.isServiceActiveByTenant
});
```

### Common Patterns

```typescript
// ✅ Correct: Tenant-aware function
export const updateTrip = onCall(async (request) => {
  const { tenantId, tripId, updates } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  const tripRef = getTenantCollection(effectiveTenantId, 'trips').doc(tripId);
  await tripRef.update(updates);
});

// ✅ Correct: Vehicle assignment with tenant validation
export const assignVehicleToDriver = onCall(async (request) => {
  const { tenantId, vehicleId, driverUID } = request.data;
  const effectiveTenantId = ensureTenantId(tenantId);

  return db.runTransaction(async (transaction) => {
    const vehicleRef = db.collection('vehicles').doc(vehicleId);
    const vehicle = await transaction.get(vehicleRef);

    // Get vehicle linking for this tenant
    const vehicleLinkingRef = getTenantCollection(effectiveTenantId, 'vehicles_linking')
      .where('vehicleId', '==', vehicleId)
      .where('isActive', '==', true);
    const linkingSnapshot = await transaction.get(vehicleLinkingRef);

    if (linkingSnapshot.empty) {
      throw new Error('Vehicle not linked to this tenant');
    }

    const linkingDoc = linkingSnapshot.docs[0];
    const linkingData = linkingDoc.data();

    if (!linkingData.tenantApproved && !linkingData.isOwnedByTenant) {
      throw new Error('Vehicle not approved for this tenant');
    }

    // Update vehicle linking assignment
    transaction.update(linkingDoc.ref, { currentDriverId: driverUID });

    // Update driver tenant state
    const tenantStateRef = db.collection('mobile_users')
      .doc(driverUID)
      .collection('tenant_states')
      .doc(effectiveTenantId);
    transaction.update(tenantStateRef, { currentVehicleLinkingId: linkingDoc.id });

    // Check for existing active assignments
    const existingAssignments = await transaction.get(
      db.collection('vehicle_assignments')
        .where('vehicleId', '==', vehicleId)
        .where('isActive', '==', true)
    );

    if (!existingAssignments.empty) {
      throw new Error('Vehicle already has an active assignment');
    }

    // Create assignment history
    const assignmentRef = db.collection('vehicle_assignments').doc();
    transaction.set(assignmentRef, {
      vehicleId,
      vehicleLinkingId: linkingDoc.id,
      driverUID,
      tenantId: effectiveTenantId,
      createdAt: FieldValue.serverTimestamp(),
      assignedAt: FieldValue.serverTimestamp(),
      assignedBy: request.auth!.uid,
      reason: 'admin_assignment',
      isActive: true
    });
  });
});

// ❌ Wrong: Hardcoded collection
export const updateTrip = onCall(async (request) => {
  const { tripId, updates } = request.data;
  const tripRef = db.collection('trips').doc(tripId); // Missing tenant context
  await tripRef.update(updates);
});

// ❌ Wrong: Non-transactional vehicle assignment
export const assignVehicle = onCall(async (request) => {
  const { vehicleId, driverUID } = request.data;
  // Missing tenant validation and transaction
  await db.collection('vehicles').doc(vehicleId).update({ currentDriverId: driverUID });
});
```

## Troubleshooting

### Common Issues

1. **"Permission denied"**: Check tenant access in admin user document
2. **"Document not found"**: Verify correct tenant ID in path
3. **"Function parameter missing"**: Ensure tenantId is passed from mobile app
4. **"Cross-tenant data leak"**: Review security rules and queries

### Debugging

```typescript
// Add logging to verify tenant context
logger.info('Function called', {
  tenantId: effectiveTenantId,
  userId: request.auth?.uid,
  operation: 'updateTrip'
});
```

## Default Tenant

The default tenant `"fiaranow"` is used for:
- Backward compatibility
- Fallback when tenant ID is missing
- Initial deployment and testing

All existing data is migrated to this default tenant during the multi-tenant transition.
